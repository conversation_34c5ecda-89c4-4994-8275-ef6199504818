
from core.application_data import ApplicationData
from models.actros import Actros
from models.man import Man
from models.scania import Scania

from commands.validation_helpers import try_parse_int, validate_params_count



class AddTruckToParkingCommand:

    def __init__(self, params, app_data: ApplicationData):
        validate_params_count(params, 2)
        self._params = params
        self._app_data = app_data

    def execute(self):
        truck_id=try_parse_int(self._params[0])
        truck_name = self._params[1]
        if truck_name == 'Actros':
            truck=Actros(truck_id, truck_name)
        elif truck_name == 'Man':
            truck=Man(truck_id,truck_name)
        elif truck_name == 'Scania':
            truck=Scania(truck_id,truck_name)
        else:
            raise ValueError(f'Invalid Truck name: {truck_name}!')
        
        self._app_data.truck_parking.create_truck(truck)
        return f'Truck: {truck_name} was added to Parking!'