import unittest
from models.test_run import TestRun
from errors.application_error import ApplicationError

VALID_TEST_RESULT = 'pass'
VALID_TEST_RUNTIME = 30
INVALID_TEST_RUNTIME = 0
class TestRun_Should(unittest.TestCase):
    def test_set_attributes(self):

        test_run = TestRun(VALID_TEST_RESULT, VALID_TEST_RUNTIME)
        self.assertEqual(VALID_TEST_RESULT, test_run.test_result)
        self.assertEqual(VALID_TEST_RUNTIME, test_run.runtime_ms)

    def test_fail_with_ApplicationError(self):
        with self.assertRaises(ApplicationError):
            test_run = TestRun(VALID_TEST_RESULT, INVALID_TEST_RUNTIME)


