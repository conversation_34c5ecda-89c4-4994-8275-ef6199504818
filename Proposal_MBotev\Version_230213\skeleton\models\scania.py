from models.truck import Truck

class Scania(Truck):

    COUNTER=1001
    def __init__(self, name: str, capacity: int, max_range: int):
        super().__init__(id, name, capacity, max_range)
        id = Scania.COUNTER
        Scania.COUNTER+=1
        if Scania.COUNTER > 1010:
            raise ValueError('No more Scania available!')
        self._name='Scania'
        self._capacity=42000
        self._max_range=8000