
def distance(start: str,end: str)->int:
        """
        This function returns the distance between two towns.
        """
        if start=='SYD' and end=='MEL' or start=='MEL' and end=='SYD':
            return 877
        elif start=='SYD' and end=='ADL' or start=='ADL' and end=='SYD':
            return 1376
        elif start=='SYD' and end=='ASP' or start=='ASP' and end=='SYD':
            return 2762
        elif start=='SYD' and end=='BRI' or start=='BRI' and end=='SYD':
            return 909
        elif start=='SYD' and end=='DAR' or start=='DAR' and end=='SYD':
            return 3935
        elif start=='SYD' and end=='PER' or start=='PER' and end=='SYD':
            return 4016
        elif start=='MEL' and end=='ADL' or start=='ADL' and end=='MEL':
            return 725
        elif start=='MEL' and end=='ASP' or start=='ASP' and end=='MEL':
            return 2255
        elif start=='MEL' and end=='BRI' or start=='BRI' and end=='MEL':
            return 1765
        elif start=='MEL' and end=='DAR' or start=='DAR' and end=='MEL':
            return 3752
        elif start=='MEL' and end=='PER' or start=='PER' and end=='MEL':
            return 3509
        elif start=='ADL' and end=='ASP' or start=='ASP' and end=='ADL':
            return 1530    
        elif start=='ADL' and end=='BRI' or start=='BRI' and end=='ADL':
            return 1927          
        elif start=='ADL' and end=='DAR' or start=='DAR' and end=='ADL':
            return 3027      
        elif start=='ADL' and end=='PER' or start=='PER' and end=='ADL':
            return 2785
        elif start=='ASP' and end=='BRI' or start=='BRI' and end=='ASP':
            return 2993
        elif start=='ASP' and end=='DAR' or start=='DAR' and end=='ASP':
            return 1497
        elif start=='ASP' and end=='PER' or start=='PER' and end=='ASP':
            return 2481
        elif start=='BRI' and end=='DAR' or start=='DAR' and end=='BRI':
            return 3426
        elif start=='BRI' and end=='PER' or start=='PER' and end=='BRI':
            return 4311
        elif start=='DAR' and end=='PER' or start=='PER' and end=='DAR':
            return 4025
            
        
        

