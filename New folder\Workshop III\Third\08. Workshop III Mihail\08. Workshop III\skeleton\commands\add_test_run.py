from commands.base.base_command import BaseCommand
from models.test_run import TestRun


class AddTestRunCommand(BaseCommand):
    def execute(self):
        test_id = int(self.params[0])
        result = self.params[1]
        runtime = int(self.params[2])

        searched_test = self.app_data.find_test_by_id(test_id)
        searched_test.add_test_run(TestRun(result, runtime))
        return 'TestRun registered'
