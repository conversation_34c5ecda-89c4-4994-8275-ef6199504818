from core.application_data import ApplicationData
from commands.validation_helpers import try_parse_int
from models.truck import Truck


class TruckGenerator:

    def __init__(self, app_data: ApplicationData):
        
        self._app_data = app_data

    def execute(self)->None:
        file_path='models/constants/truck_data.txt'
        with open(file_path, mode='r') as truck_file:
            all_trucks=truck_file.readlines()

        for truck_data in all_trucks:
            # truck_id = try_parse_int(truck_data.split(' ')[0])
            truck_name = truck_data.split(' ')[1]
            capacity = try_parse_int(truck_data.split(' ')[2])
            max_range = try_parse_int(truck_data.split(' ')[3])

            truck = Truck(truck_name, capacity, max_range)  # Using Truck class directly

            self._app_data.truck_parking.create_truck(truck)
        # return all_trucks