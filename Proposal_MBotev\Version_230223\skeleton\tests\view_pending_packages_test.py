import unittest
from unittest.mock import MagicMock
from core.application_data import ApplicationData
from models.package import Package
from commands.view_pending_packages import ViewPendingPackagesCommand

class TestViewPendingPackagesCommand(unittest.TestCase):
    def test_execute(self):
        app_data = ApplicationData()
        mock_package = MagicMock(spec=Package)
        app_data.pending_packages.create_package(mock_package)
        expected_result = f'{app_data.pending_packages}'
        command = ViewPendingPackagesCommand([], app_data)
        result = command.execute()
        self.assertEqual(result, expected_result)
