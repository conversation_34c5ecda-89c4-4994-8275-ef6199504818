import unittest
from commands.validation_helpers import validate_params_count, try_parse_float, try_parse_int

class ValidationHelpersTest(unittest.TestCase):

    def test_validate_params_count_correct_count(self):
        params = ['param1', 'param2', 'param3']
        count = 3
        self.assertIsNone(validate_params_count(params, count))

    def test_validate_params_count_incorrect_count(self):
        params = ['param1', 'param2']
        count = 3
        with self.assertRaises(ValueError):
            validate_params_count(params, count)

    def test_try_parse_float_valid_float(self):
        s = '3.14'
        expected = 3.14
        self.assertEqual(try_parse_float(s), expected)

    def test_try_parse_float_invalid_float(self):
        s = 'not_a_float'
        with self.assertRaises(ValueError):
            try_parse_float(s)

    def test_try_parse_int_valid_int(self):
        s = '42'
        expected = 42
        self.assertEqual(try_parse_int(s), expected)

    def test_try_parse_int_invalid_int(self):
        s = 'not_an_int'
        with self.assertRaises(ValueError):
            try_parse_int(s)
