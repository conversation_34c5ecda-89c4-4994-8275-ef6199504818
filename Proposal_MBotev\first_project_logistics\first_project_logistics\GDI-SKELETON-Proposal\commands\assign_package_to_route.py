from core.application_data import ApplicationData
# from models.package import Package
# from models.route import Route
# from models.truck import Truck

class AssignPackageToRouteCommand:

    def __init__(self, params, app_data: ApplicationData):
        self._params = params
        self._app_data = app_data

    def execute(self):
        route_id, package_id = self._params
        route = self._app_data.find_route_by_id(route_id)
        package = self._app_data.find_package_by_id(package_id)

        if route is None:
            return f'Route with ID: {route_id} not found.'
        if package is None:
            return f'Package with ID: {package_id} not found.'

        if package.route is not None: # Does it work?
            return f'Package with ID: {package_id} is already assigned to Route with ID: {package.route.id}.'

        if not route.can_add_package(package):# Does it work?
            return f'Route with ID: {route_id} has no space for Package with ID: {package_id}.'

        route.add_package(package)
        package.route = route # Does it work?
        return f'Package with ID: {package_id} was assigned to Route with ID: {route_id}.'



# from core.application_data import ApplicationData
# # from commands.validation_helpers import validate_params_count


# class AssignPackageToRouteCommand:

#     def __init__(self, params, app_data: ApplicationData):
#         # validate_params_count(params,1)
#         self._params = params
#         self._app_data = app_data

#     def execute(self):
#         route_id = int(self._params[0])
#         package_id = int(self._params[1])
#         self._app_data.assign_package(route_id,package_id)
        
#         return f'Package with ID: {package_id}  was assigned to Route with ID: {route_id}!'
    
    
