import csv
from models.route import Route
from models.package import Package

def export_data_to_csv(file_name, data_list):
    with open(file_name, mode='w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(['Route ID', 'Departure Time', 'Locations', 'Assigned Truck'])
        for data in data_list:
            writer.writerow([data.id, data.departure_time, data.locations, data.truck.id if data.truck else ''])

def export_routes_to_csv(file_name, routes):
    export_data_to_csv(file_name, routes)

def export_packages_to_csv(file_name, packages):
    export_data_to_csv(file_name, packages)
