
from src.linked_list_node import LinkedListNode


class CustomStack:
    def __init__(self) -> None:
        self._top : LinkedListNode = None
        self.count = 0
        self.is_empty = True

    def push(self,value):
        self._top = LinkedListNode(value,self._top)
        self.count+=1
        if self.is_empty is True and self.count>0:
            self.is_empty = False

    def pop(self):
        if self.is_empty is True:
            raise ValueError('No elements to pop in the stack')
        popped_node = self._top
        self._top = self._top.next
        self.count -= 1
        if self.is_empty is not True and self.count == 0:
            self.is_empty = True
        return popped_node.value

    def peek(self):
        if self.is_empty is False:
            return self._top.value
        raise ValueError('No elements in the stack')
    

    


    
    
