from models.truck import Truck

class Scania(Truck):

    # COUNTER=1001
    def __init__(self, id: int,name: str, capacity=42000, max_range=8000):
        super().__init__(id, name, capacity, max_range)
        # id = Scania.COUNTER
        # Scania.COUNTER+=1
        if id < 1001:
            raise ValueError('The Scania id cannot be less than 1001')
        
        if id > 1010:
            raise ValueError('The Scania id cannot be more than 1010')
        # self._name='Scania'
        # self._capacity=42000
        # self._max_range=8000