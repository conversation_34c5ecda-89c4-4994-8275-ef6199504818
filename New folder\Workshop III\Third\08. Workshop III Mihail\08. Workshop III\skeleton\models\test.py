from models.constants.test_result import TestResult
from models.test_run import TestRun


class Test:
    test_id = 0

    @classmethod
    def next_test_id(cls):
        cls.test_id += 1
        return cls.test_id

    def __init__(self, id: int, description: str):
        self._id = Test.next_test_id()
        self._description = description
        self._test_runs: list[TestRun] = []
        self._passed_tests = 0
        self._failed_tests = 0
        self._total_runtime = 0

    @property
    def id(self):
        return self._id

    @property
    def description(self):
        return self._description

    @property
    def test_runs(self):
        return tuple(self._test_runs)

    @property
    def passed_tests(self):
        return self._passed_tests

    @property
    def failed_tests(self):
        return self._failed_tests

    def add_test_run(self, test_run: TestRun):
        self._test_runs.append(test_run)
        self._total_runtime += test_run.runtime_ms
        if test_run.test_result == TestResult.PASS:
            self._passed_tests += 1
        else:
            self._failed_tests += 1

    def test_report(self):
        return f'#{self.id}. [{self.description}]: {len(self.test_runs)} runs\n' \
        f'- Passing: {self.passed_tests}\n' \
        f'- Failing: {self.failed_tests}\n' \
        f'- Total runtime: {self._total_runtime}ms\n' \
        f'- Average runtime: {self._total_runtime / len(self.test_runs):.1f}ms'

    def test_to_string(self):
        return f'  #{self.id}. [{self.description}]: {len(self.test_runs)} runs'
