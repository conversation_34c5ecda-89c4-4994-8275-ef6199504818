from models.test_group import TestGroup


class ApplicationData:
    def __init__(self):
        self._test_groups: list[TestGroup] = []
        self._test = []
    @property
    def groups(self):
        return tuple(self._test_groups)

    def find_test_group_by_id(self, test_group_id: int):
        for test_group in self._test_groups:
            if test_group.id == test_group_id:
                return test_group
        return None

    def find_test_by_id(self, test_id):
        for test_group in self._test_groups:
            for test in test_group._tests:
                if test.id == test_id:
                    return test
        return None