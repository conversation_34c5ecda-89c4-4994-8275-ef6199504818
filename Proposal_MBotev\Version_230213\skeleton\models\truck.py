# from models.route import Route
# from models.package import Package
class Truck:
    def __init__(self, id, name, capacity, max_range):
        self._id = id
        self._name = name
        self._capacity = capacity
        self._max_range = max_range
        # self.num_of_vehicles = num_of_vehicles
        self._delivery_routes: list = []
        self._delivery_packages: list = []

    # def assign_route(self, delivery_route):
    #     if delivery_route.distance > self.max_range:
    #         raise ValueError("The delivery route is too far for this truck")
    #     self.delivery_routes.append(delivery_route)
    #     delivery_route.truck = self

    # def assign_package(self, delivery_package):
    #     if delivery_package.weight > self.capacity:
    #         raise ValueError("The delivery package is too heavy for this truck")
    #     self.delivery_packages.append(delivery_package)
    #     delivery_package.truck = self

    @property
    def id(self):
        return self._id
    
    @property
    def name(self):
        return self._name

    @property
    def capacity(self):
        return self._capacity

    @property
    def max_range(self):
        return self._max_range

    # @property
    # def num_of_vehicles(self):
    #     return self._num_of_vehicles

    @property
    def delivery_routes(self):
        return tuple(self._delivery_routes)

    @property
    def delivery_packages(self):
        return tuple(self._delivery_packages)

    def assign_route(self, delivery_route)->None:
        if delivery_route.total_distance > self._max_range:
            raise ValueError("The delivery route is too far for this truck")
        self._delivery_routes.append(delivery_route)

    def assign_package(self, delivery_package):
        if delivery_package.weight > self._capacity:
            raise ValueError("The delivery package is too heavy for this truck")
        self._delivery_packages.append(delivery_package)
        
    def add_route(self, delivery_route) -> None:
        if delivery_route.total_distance > self._max_range:
            raise ValueError("The delivery route is too far for this truck")
        self._delivery_routes.append(delivery_route)
        delivery_route.add_truck(self)

    def add_package(self, delivery_package) -> None:
        if delivery_package.weight > self._capacity:
            raise ValueError("The delivery package is too heavy for this truck")
        self._delivery_packages.append(delivery_package)
        # delivery_package.add_truck(self)

    def __str__(self) -> str:
        return '\n'.join([
                f'Truck ID: {self.id}',
                f'Name: {self._name}',
                f'Capacity: {self._capacity}',
                f'Max range: {self._max_range}',
        ])
