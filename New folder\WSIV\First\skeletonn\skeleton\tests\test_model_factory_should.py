import unittest
import tests.test_data as td
from core.models_factory import ModelsFactory
from models.constants.test_result import TestResult
from errors.application_error import ApplicationErrorRuntime, ApplicationErrorResult



class ModelFactory_Should(unittest.TestCase):
    def test_canInstantiate(self):
        models_factory = ModelsFactory()
        self.assertEqual(models_factory._test_group_id, 1)
        self.assertEqual(models_factory._test_id, 1)

    def test_createGroupMethod_returnsCorrect(self):
        models_factory = ModelsFactory()
        group = models_factory.create_group(td.VALID_GROUP_NAME)
        
        self.assertEqual(group.name, td.VALID_GROUP_NAME)
        self.assertEqual(group.id, 1)

        group = models_factory.create_group(td.VALID_GROUP_NAME_TWO)

        self.assertEqual(group.name, td.VALID_GROUP_NAME_TWO)
        self.assertEqual(group.id, 2)

    def test_createTestMethod_returnsCorrect(self):
        models_factory = ModelsFactory()
        test = models_factory.create_test(td.VALID_TEST_DESCRIPTION)

        self.assertEqual(test.id, 1)
        self.assertEqual(test.description, td.VALID_TEST_DESCRIPTION)

        test = models_factory.create_test(td.VALID_TEST_DESCRIPTION_TWO)
        self.assertEqual(test.id, 2)
        self.assertEqual(test.description, td.VALID_TEST_DESCRIPTION_TWO)

    def test_createTestRunMethod_successfulCreatePASS(self):
        models_factory = ModelsFactory()
        test_result = TestResult("pass")
        test_runtime_ms = int("1")
        test_run = models_factory.create_test_run(test_result, test_runtime_ms)
        self.assertEqual(test_run.test_result, TestResult.PASS)
        self.assertEqual(test_run.runtime_ms, 1)

    def test_createTestRunMethod_successfulCreateFAIL(self): 
        models_factory = ModelsFactory()
        test_result = TestResult("fail")
        test_runtime_ms = int("1")
        test_run = models_factory.create_test_run(test_result, test_runtime_ms)
        self.assertEqual(test_run.test_result, TestResult.FAIL)
        self.assertEqual(test_run.runtime_ms, 1)
        

    def test_createTestRunMethod_raisesError_invalidResult(self):
        models_factory = ModelsFactory()
        with self.assertRaises(ApplicationErrorResult) as err:
            models_factory.create_test_run(td.INVALID_VALUE, td.VALID_RUNTIME)
            self.assertEqual(str(err.exception), 'Invalid value for result')

    def test_createTestRunMethod_raisesError_invalidRuntime(self):
        models_factory = ModelsFactory()
        with self.assertRaises(ApplicationErrorRuntime) as err:
            models_factory.create_test_run(td.VALID_TEST_RESULT, td.INVALID_VALUE)
            self.assertEqual(str(err.exception), 'Invalid value for runtime_ms')



