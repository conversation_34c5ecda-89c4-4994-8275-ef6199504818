import unittest
from core.pending_packages import PendingPackages
from models.package import Package

VALID_START_LOCATION='SYD'
VALID_END_LOCATION='MEL'
VALID_WEIGHT=23.0
VALID_CONTACT_INFO='<PERSON>, 23, Silver route, Melbourne'
EXPECTED_OUTPUT_NO_PACKAGE='/!\ No pending packages. Have a break!'
EXPECTED_OUTPUT_YES_PACKAGE='  Pending Packages Information:\n'+'#Package ID:1 | Start location: SYD | End location: MEL'

class PendingPackages_Should(unittest.TestCase):
    def test_initializer_setsPropertyCorrectly(self):
        p_p=PendingPackages()

        self.assertIsInstance(p_p.pending_packages,tuple)

    def test_createpackage_AddSPAckages(self):
        p_p=PendingPackages()
        package=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)

        p_p.create_package(package)

        self.assertEqual(1,len(p_p.pending_packages))

    def test_findpackage_by_id_ReturnsPackageIfExists(self):
        p_p=PendingPackages()
        package=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)
        p_p.create_package(package)

        package=p_p.find_package(1)

        self.assertIsInstance(package,Package)

    def test_findpackage_by_idRaisedValueErrorIfNotExist(self):
        p_p=PendingPackages()
        package=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)
        p_p.create_package(package)
        p_p.remove_package(package)

        with self.assertRaises(ValueError):
            p_p.find_package(1)


    def test_removepackage_RemovesPackage(self):
        p_p=PendingPackages()
        package=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)
        p_p.create_package(package)

        p_p.remove_package(package)

        self.assertEqual(0,len(p_p.pending_packages))

    def test_str_NoPackages(self):
        p_p=PendingPackages()
        package=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)
        p_p.create_package(package)
        p_p.remove_package(package)

        self.assertEqual(EXPECTED_OUTPUT_NO_PACKAGE,str(p_p))

    def test_str_PackagesExist(self):
        p_p=PendingPackages()
        package=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)
        p_p.create_package(package)

        self.assertEqual(EXPECTED_OUTPUT_YES_PACKAGE,str(p_p))

