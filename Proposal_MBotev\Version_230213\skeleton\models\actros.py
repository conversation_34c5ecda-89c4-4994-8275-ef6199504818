from models.truck import Truck

class Actros(Truck):

    COUNTER=1026
    def __init__(self, name: str, capacity: int, max_range: int):
        super().__init__(id, name, capacity, max_range)
        id = Actros.COUNTER
        Actros.COUNTER+=1
        if Actros.COUNTER > 1040:
            raise ValueError('No more Actros availbale!')
        self._name='Actros'
        self._capacity=26000
        self._max_range=13000