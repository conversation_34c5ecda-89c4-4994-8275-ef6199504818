from models.test_group import TestGroup
from core.application_data import ApplicationData

class ViewSystem():
    def __init__(self, app_data: ApplicationData) -> None:
        self._app_data = app_data

    def execute(self):
        output = f'Test Reporter System ({len(self._app_data._test_groups)} test groups)'
        for test_group in self._app_data._test_groups:
            output += f'\n  #{test_group._id}. {test_group._name} ({len(test_group._tests)} tests)'
            

        
        return output
        # test_id = self._app_data.find_test_by_id()
        # test_group_id = self._app_data















'''
ViewSystem (no params) - returns formatted information about the Test Reporter System. Formatting:

Test Reporter System ({test_groups_count} test groups)
  #{group_id}. {group_name} ({tests_count} tests)
  #{group_id}. {group_name} ({tests_count} tests)
'''