from core.application_data import ApplicationData
from commands.base_command import BaseCommand


class TestReport(BaseCommand):
    def __init__(self, params: list[str], app_data: ApplicationData):
        super().__init__(params, app_data)

    def execute(self):
        test_id = int(self._params[0])
        test_report = self._app_data.find_test_by_id(test_id)
        passing_runs_count = 0
        failing_runs_count = 0
        total_runtime = 0

        for test_run in test_report._test_runs:
            total_runtime += test_run._runtime_ms
            if test_run._test_result == "pass":
                passing_runs_count += 1
            if test_run._test_result == "fail":
                failing_runs_count += 1

        average_runtime = total_runtime / len(test_report._test_runs)

        return f'#{test_id}. [{test_report._description}]: {len(test_report._test_runs)} runs\n' \
               f'- Passing: {passing_runs_count}\n' \
               f'- Failing: {failing_runs_count}\n' \
               f'- Total runtime: {total_runtime}ms\n' \
               f'- Average runtime: {average_runtime:.1f}ms'








'''
TestReport (params: test_id) - returns formatted information about
a test with the given test_id. Formatting:
#{test_id}. [{test_description}]: {test_runs_count} runs
- Passing: {passing_runs_count}
- Failing: {failing_runs_count}
- Total runtime: {total_runtime}ms
- Average runtime: {avg_runtime:.1f}ms
'''