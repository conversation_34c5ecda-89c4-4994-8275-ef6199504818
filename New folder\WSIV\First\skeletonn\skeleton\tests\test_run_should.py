import unittest
from models.test_run import TestRun
from errors.params_error import InvalidParamsTestRunClass
import tests.test_data as td



class TestRun_Should(unittest.TestCase):
    def test_canInstantiate(self):
        test_run = TestRun(td.VALID_TEST_RESULT.value, td.VALID_RUNTIME)
        
    def test_initializer_setsValuesValid(self):
        # Arrange
        # Act
        test_run = TestRun(td.VALID_TEST_RESULT.value, td.VALID_RUNTIME)
        # Assert
        self.assertEqual(td.VALID_TEST_RESULT.value, test_run.test_result)
        self.assertEqual(td.VALID_RUNTIME, test_run.runtime_ms)

    def test_constructor_raisesErrorWhen_negativeOrZeroRuntimeMs(self):
        with self.assertRaises(InvalidParamsTestRunClass):
            test_run = TestRun(td.VALID_TEST_RESULT.value, (td.NEGATIVE_RUNTIME or td.ZERO_RUNTIME))


