# from tkinter import *

# def hello(event):
#     print('Hello World')

# root=Tk()
# but=Button(root,
#            text='Click me',
#            width=30, height=5,
#            bg='white', fg='blue')

# label=Label(root,
#             text='Entrer you name \n NAME.',
#              font='Arial 16' )
# ent=Entry(root,
#           width=20,
#           bd=3)
# tex=Text(root,
#          font='Arial 12',
#          wrap=CHAR)

# label1=Label(root,
#             text='Entrer what you think.',
#              font='Arial 12' )

# var=IntVar()
# var.set(0)
# rad0=Radiobutton(root,text='Windows',variable=var,value=0)
# rad1=Radiobutton(root,text='Linux',variable=var,value=1)
# rad2=Radiobutton(root,text='macOs',variable=var,value=2)

# c1=IntVar()
# c2=IntVar()
# che1=Checkbutton(root, text='First chekb', variable=c1, onvalue=1, offvalue=0)
# che2=Checkbutton(root, text='Second chekb', variable=c2, onvalue=2, offvalue=0)

# r=['Audi','BMW','Lexus','Toyota']
# lis=Listbox(root, selectmode=SINGLE, height=4)
# for i in r:
#     lis.insert(END,i)
# but.pack()
# label.pack()
# ent.pack()
# label1.pack()
# tex.pack()
# rad0.pack()
# rad1.pack()
# rad2.pack()
# che1.pack()
# che2.pack()
# lis.pack()
# root.mainloop()


# def outer():
#     n = 10
#     def inner():
#         print(n)
#     inner()

# outer()

# from datetime import datetime, timedelta
# a=datetime(2023,7,18)
# a1=a+timedelta(days=60)
# a2=a+timedelta(days=100)
# print(a1)
# print(a2)


import sys
reload(sys)
sys.setdefaultencoding('utf8')
a='Текст utf8'
b=u'Текст unicode'
c=a+b
print('a=',type(a),a)
print('b=',type(b),b)
print('c=',type(c),c)

