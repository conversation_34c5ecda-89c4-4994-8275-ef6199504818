import unittest
from unittest.mock import MagicMock, patch
from core.application_data import ApplicationData
from commands.view_package_in_route import ViewPackageInRouteCommand

class TestViewPackageInRouteCommand(unittest.TestCase):

    def test_execute(self):
        app_data = MagicMock(spec=ApplicationData)
        app_data.route_containg_package.return_value = 'Route 1'
        route = MagicMock()
        route.current_situation.return_value = 'In transit'
        app_data.route_containg_package.return_value = route
        cmd = ViewPackageInRouteCommand(['1', '2023-03-01', '12:00:00'], app_data)
        result = cmd.execute()
        app_data.route_containg_package.assert_called_with(1)
        route.current_situation.assert_called_with(1, '2023-03-01 12:00:00')
        self.assertEqual(result, 'Package with ID:1, travelling on Route 1. Current state: In transit')
        
    def test_execute_no_route(self):
        app_data = MagicMock(spec=ApplicationData)
        app_data.route_containg_package.return_value = None
        cmd = ViewPackageInRouteCommand(['1', '2023-03-01', '12:00:00'], app_data)
        with self.assertRaises(ValueError):
            cmd.execute()
        app_data.route_containg_package.assert_called_with(1)
        
if __name__ == '__main__':
    unittest.main()
