from commands.create_package import C<PERSON><PERSON><PERSON><PERSON><PERSON>ommand
from commands.create_route import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from commands.search_route import Search<PERSON>oute<PERSON>ommand
from commands.update_route import UpdateRouteCommand
# from commands.assign_package_to_route import AssignPackageToRouteCommand
from commands.view_information import ViewInformationCommand

class CommandFactory:
    def __init__(self, data):
        self._app_data = data

    def create(self, input_line):
        cmd, *params = input_line.split()

        if cmd.lower() == "createpackage":
            return CreatePackageCommand(params, self._app_data)
        if cmd.lower() == "createroute":
            return CreateRouteCommand(params, self._app_data)
        if cmd.lower() == "searchroute":
            return SearchRouteCommand(params, self._app_data)
        if cmd.lower() == "updateroute":
            return UpdateRouteCommand(params, self._app_data)
        # if cmd.lower() == "assignpackagetoroute":
        #     return AssignPackageToRouteCommand(params, self._app_data)
        if cmd.lower() == "viewinformation":
            return ViewInformationCommand(params, self._app_data)
        
        raise ValueError(f'Invalid command name: {cmd}!')
    
    
    
    
    #Can we not use the assign_package_to_route - While using only the update_route method?