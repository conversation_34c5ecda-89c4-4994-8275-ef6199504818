import unittest
from datetime import datetime
from core.application_data import ApplicationData
from commands.assign_package_to_route import AssignPackageToRouteCommand
from models.route import Route
from models.package import Package


class TestAssignPackageToRouteCommand(unittest.TestCase):

    def setUp(self):
        self.app_data = ApplicationData()
        self.package = Package('Location 1', 'Location 2', 10, 'John')
        self.app_data.pending_packages.create_package(self.package)
        self.route = Route(datetime(2022, 2, 16, 8), 'Location 1 -> Location 2', 1000)
        self.app_data.add_route(self.route)

    def test_execute_assigns_package_to_route(self):
        command = AssignPackageToRouteCommand([str(self.route.id), str(self.package.id)], self.app_data)
        command.execute()
        self.assertIn(self.package, self.route.packages)

    def test_execute_raises_error_if_route_not_found(self):
        command = AssignPackageToRouteCommand(['999', str(self.package.id)], self.app_data)
        with self.assertRaises(ValueError):
            command.execute()

    def test_execute_raises_error_if_package_not_found(self):
        command = AssignPackageToRouteCommand([str(self.route.id), '999'], self.app_data)
        with self.assertRaises(ValueError):
            command.execute()

    def test_execute_raises_error_if_route_weight_capacity_exceeded(self):
        self.route.weight_capacity = 5
        command = AssignPackageToRouteCommand([str(self.route.id), str(self.package.id)], self.app_data)
        with self.assertRaises(ValueError):
            command.execute()

    def test_execute_removes_package_from_pending_packages(self):
        command = AssignPackageToRouteCommand([str(self.route.id), str(self.package.id)], self.app_data)
        command.execute()
        self.assertNotIn(self.package, self.app_data.pending_packages.packages)


if __name__ == '__main__':
    unittest.main()
