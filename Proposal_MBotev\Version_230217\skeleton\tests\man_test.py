import unittest
from models.man import Man

VALID_NAME='Man'
VALID_CAPACITY=23000
VALID_MAX_RANGE=10000

class ManShould(unittest.TestCase):

    def test_initializer_RaisesErrorWhenIdIsLower(self):
        with self.assertRaises(ValueError):
            man=Man(1010,VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)

    def test_initializer_RaisesErrorWhenIdIsHigher(self):
        with self.assertRaises(ValueError):
            man=Man(1026,VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)