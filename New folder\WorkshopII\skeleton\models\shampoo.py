from models.product import Product


class Shampoo(Product):
    def __init__(self, name, brand, price, gender, usage_type, milliliters):
        super().__init__(name, brand, price, gender)

        self._usage_type = usage_type
        self._milliliters = milliliters

    @property
    def name(self):
        return self._name

    @name.setter
    def name(self, value):
        if len(value) < 3 or len(value) > 10:
            raise ValueError('Name should be between 3 and 10 symbols.')

        self._name = value

    @property
    def brand(self):
        return self._brand

    @brand.setter
    def brand(self, value):
        if len(value) < 2 or len(value) > 10:
            raise ValueError('Brand should be between 2 and 10 symbols.')

        self._brand = value

    @property
    def price(self):
        return self._price

    @price.setter
    def price(self, value):
        if value < 0:
            raise ValueError('Price should not be negative.')

        self._price = value

    @property
    def gender(self):
        return self._gender

    
    
    @property
    def milliliters(self):
        return self._milliliters
    
    @milliliters.setter
    def milliliters(self, value):
        if value < 0:
            raise ValueError('Milliliters should not be negative.')

        self._milliliters = value

    @property
    def usgetype(self):
        return self._usage_type
    
    def to_string(self):
     return f"{super().to_string()} \n Milliliters: {self._milliliters} \n Usage: {self._usage_type}"


