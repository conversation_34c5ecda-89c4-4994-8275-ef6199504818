from core.application_data import ApplicationData
from commands.add_test_group import AddTestGroupCommand
from commands.add_test import AddTestCommand
from commands.add_test_run import AddTestRunCommand
from commands.test_report import TestReport
from commands.view_group import ViewGroup
from commands.view_system import ViewSystem
from commands.remove_group import RemoveGroup


class CommandFactory:
    def __init__(self, data: ApplicationData):
        self._app_data = data

    def create(self, input_line: str):
        cmd, *params = input_line.split()

        if cmd == 'addtestgroup':
            return AddTestGroupCommand(params, self._app_data)
        if cmd == 'addtest':
            return AddTestCommand(params, self._app_data)
        if cmd == 'addtestrun':
            return AddTestRunCommand(params, self._app_data)
        if cmd == 'testreport':
            return TestReport(params, self._app_data)
        if cmd == 'viewgroup':
            return ViewGroup(params, self._app_data)
        if cmd == 'viewsystem':
            return ViewSystem(params, self._app_data)
        if cmd == 'removegroup':
            return RemoveGroup(params, self._app_data)
