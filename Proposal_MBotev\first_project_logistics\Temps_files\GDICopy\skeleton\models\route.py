from datetime import datetime, timedelta
from constants.distance import distance



class Route:
    COUNTER=1
    FORMAT="%b %dth %H:%Mh"
    

    def __init__(self, departure_time: str, locations: str) -> None:
        self._id=Route.COUNTER
        Route.COUNTER+=1
        self._locations=locations.split('->')
        self._start_location=self._locations[0]
        self._departure_time=datetime.strptime(departure_time, Route.FORMAT )
        self._end_location=self._locations[-1]
        
        self._mid_stop1=[x for x in self._locations if self._locations.index(x)==1]
        self._mid_stop2=[x for x in self._locations if self._locations.index(x)==2]
        self._mid_stop3=[x for x in self._locations if self._locations.index(x)==3]
        self._mid_stop4=[x for x in self._locations if self._locations.index(x)==4]
        self._mid_stop5=[x for x in self._locations if self._locations.index(x)==5]
        
        self._packages: list=[]


    @property
    def number_mid_stops(self):
        return len(self._locations)-2
    
    
    def route_distance(self):

        if self.number_mid_stops==0:
            self.distance1=distance(self._start_location,self._end_location)
            return self.distance1
        
        elif self.number_mid_stops==1:
            self.distance1=distance(self._start_location,*self._mid_stop1)
            self.distance2=distance(self._end_location,*self._mid_stop1)
            return self.distance1,self.distance2
        
        elif self.number_mid_stops==2:
            self.distance1=distance(self._start_location,*self._mid_stop1)
            self.distance2=distance(*self._mid_stop1,*self._mid_stop2)
            self.distance3=distance(self._end_location,*self._mid_stop2)
            return self.distance1,self.distance2,self.distance3
    
    

    def end_time(self,start_time,distance):
        return start_time+timedelta(hours=(distance/87+3.56))
        
    
    def info_route(self):
        formated_departure_time=datetime.strftime(self._departure_time,Route.FORMAT)
        self._mid_stop1_time=self.end_time(self._departure_time,self.route_distance()[0])
        formated_mid_stop1_time=datetime.strftime(self._mid_stop1_time,Route.FORMAT)

        self._end_location_time=self.end_time(self._mid_stop1_time,self.route_distance()[1])
        formated_end_location_time=datetime.strftime(self._end_location_time,Route.FORMAT)
        return (f'Route ID: {self._id} / {self._start_location} ({formated_departure_time})'
                f'-> {self._mid_stop1[0]} ({formated_mid_stop1_time})'
                f'-> {self._end_location} ({formated_end_location_time})')
    
        
    




    

first_route=Route('Oct 10th 06:00h','BRI->SYD->MEL')


# print(first_route.end_time(909))
# print(first_route.number_mid_stops)
# print(first_route._start_location)
# print(first_route._end_location)
# print(first_route._mid_stop1)

# print(first_route.route_distance())
print(first_route.info_route())




# try:
#     print(first_route._mid_location2)
# except:
#     print('No more mid_locations')