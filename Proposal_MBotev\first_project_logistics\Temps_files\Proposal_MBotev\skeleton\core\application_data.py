from models.route import Route
from models.pending_packages import PendingPackages



class ApplicationData:
    def __init__(self):
        self._routes: list[Route] = []
        self._pending_packages=PendingPackages()
        

    @property
    def routes(self):
        return tuple(self._routes)
    
    @property
    def pending_packages(self):
        return self._pending_packages

    def search_route(self, start, end) -> list[Route]:
        founded_routes = []
        for route in self._routes:
            if start in route.locations:
                if end in route.locations:
                    if route.locations.index(start) < route.locations.index(end):
                        founded_routes.append(route)
        return '\n'.join(f'{route}' for route in founded_routes)

    def add_route(self, route: Route) -> None:
        self._routes.append(route)

    

    def find_route(self,route_id: int)->Route|None:
        founded=[route for route in self._routes if route.id == route_id]
        if founded == []:
            return None
        return founded[0]
    
    
    
    def assign_package(self,route_id: int, package_id: int)->None:
        found_package=self._pending_packages.find_package(package_id)
        for r in self._routes:
            if r.id == route_id:
                r._packages.append(found_package)
                self._pending_packages.remove_package(found_package)
        # return f'Package with ID: {package_id} assigned to route wiht ID: {route_id}'
    
    def assign_truck(self):
        pass
        


# app_data = ApplicationData()

# first_route = Route('Oct 10th 06:00h', 'BRI->SYD->MEL')
# app_data.add_route(first_route)
# second_route = Route('Oct 12th 06:00h', 'SYD->MEL->ADL')
# app_data.add_route(second_route)

# print(app_data.routes)
# print(app_data.search_route('MEL','ADL'))

    

    