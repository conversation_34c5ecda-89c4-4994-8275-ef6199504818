class Truck:
    def __init__(self, id, name, capacity, max_range, num_of_vehicles):
        self.id = id
        self.name = name
        self.capacity = capacity
        self.max_range = max_range
        self.num_of_vehicles = num_of_vehicles
        self.delivery_routes = []
        self.delivery_packages = []

    def assign_route(self, delivery_route):
        if delivery_route.distance > self.max_range:
            raise ValueError("The delivery route is too far for this truck")
        self.delivery_routes.append(delivery_route)
        delivery_route.truck = self

    def assign_package(self, delivery_package):
        if delivery_package.weight > self.capacity:
            raise ValueError("The delivery package is too heavy for this truck")
        self.delivery_packages.append(delivery_package)
        delivery_package.truck = self