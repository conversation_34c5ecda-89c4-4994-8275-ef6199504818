from models.product import Product


class Toothpaste(Product):
    def __init__(self, name: str, brand: str, price: float, gender: str, ingredients: list) -> None:
        super().__init__(name, brand, price, gender)

        self._ingredients = ingredients


    @property
    def ingredients(self):
        return tuple(self._ingredients)

    def to_string(self) -> str:
        ingredients_string = ', '.join(self.ingredients)
        return '\n'.join([
        f' #{self.name} {self.brand}',
        f' #Price: ${self.price:.2f}',
        f' #Gender: {self.gender}',
        f' #Ingredients: {ingredients_string}',
    ])