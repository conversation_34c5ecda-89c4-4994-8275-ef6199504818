import unittest
from core.command_factory import CommandFactory
from commands.add_test_group import AddTest<PERSON>roupCommand
from commands.add_test_run import AddTestRunCommand
from commands.remove_group import RemoveGroupCommand
from commands.test_report import TestReportCommand
from commands.view_group import ViewGroupCommand
from commands.view_system import ViewSystemCommand
from core.application_data import ApplicationData
from commands.add_test import AddTestCommand
from core.models_factory import ModelsFactory
from test import test_data
from models.test_run import TestRun
from models.test import Test
from models.test_group import TestGroup
from models.constants.test_result import TestResult
from errors.application_error import ApplicationError
app_data = ApplicationData()
cmd_factory = CommandFactory(app_data)


class Testrun_Should(unittest.TestCase):
    def test_init_setProperties(self):
        # Arrange & Act
        testrun=TestRun(VALID_RESULT,VALID_RUNTIME)
        # Assert
        self.assertEqual(VALID_RESULT, testrun.test_result)
        self.assertEqual(VALID_RUNTIME, testrun.runtime_ms)

    def test_constructor_raisesError_when_runtime_isNegative(self):
        # Arrange, Act & Assert
        with self.assertRaises(ApplicationError):
            _=TestRun(VALID_RESULT,-5)

class Test_Should(unittest.TestCase):
    def test_init_setProperties(self):
        # Arrange & Act
        test=Test(VALID_ID,VALID_DESCRIPTION)
        # Assert
        self.assertEqual(VALID_ID, test.id)
        self.assertEqual(VALID_DESCRIPTION, test.description)
        self.assertIsInstance(test.test_runs, tuple)

    def test_constructor_raisesError_when_DescriptionIsNone_orEmpty(self):
        with self.assertRaises(AppApplicationError)
            _=Test(VALID_ID,"")
        with self.assertRaises(AppApplicationError)
            _=Test(VALID_ID,None)

    # def  test_constructor_displayesMessage_whenApplicationerror(self):
    #     _ = Test(VALID_ID, "")


    def test_constructor_initializesCorrectTestrunsType_when_Created(self):
        # Arrange
        test=Test(VALID_ID,VALID_DESCRIPTION)

        # Act & Assert
        self.assertIsInstance(test.test_runs, tuple)

    def test_Generatereport_returnsCorrectlyFormattedString(self):
        # Arrange
        test = Test(VALID_ID, VALID_DESCRIPTION)

        # Act & Assert
        self.assertEqual(EXPECTED_OUTPUT, test.generate_report())

    def test_str_returnsCorrectlyFormatted(self):
        # Arrange
        test = Test(VALID_ID, VALID_DESCRIPTION)
        expected= f'#{VALID_ID}. {VALID_DESCRIPTION}: {len(VALID_TESTRUNS)} runs'
        # Act
        stringified = str(test)

        # Assert
        self.assertEqual(expected, stringified)

    def test_add_test_run_adds_aTestRun(self):
        # Arrange
        test = Test(VALID_ID, VALID_DESCRIPTION)
        testrun = TestRun(VALID_RESULT, VALID_RUNTIME)

        # Act
        test.add_test_run(testrun)

        # Assert
        self.assertEqual(tuple([testrun]), test.test_runs)

    def test_passing_test_runsReturns_emptyTuple(self):
        # Arrange
        test = Test(VALID_ID, VALID_DESCRIPTION)
        testrun = TestRun(VALID_RESULT, VALID_RUNTIME)
        testrun.test_result==TestResult.FAIL

        # Act
        test.add_test_run(testrun)


        # Assert
        self.assertEqual(tuple(), test.test_runs)

class Test_Group_Should(unittest.TestCase):
    def test_constructor_raisesError_when_nameIsEmpty(self):
       # Arrange, Act & Assert
       with self.assertRaises(ApplicationError):
          _ = TestGroup('')

    def test_constructor_raisesError_when_nameIsNone(self):
        # Arrange, Act & Assert
        with self.assertRaises(ApplicationError):
            _ = TestGroup(None)

    def test_init_setProperties_correctly(self):
        # Arrange & Act
        testgroup=TestGroup(VALID_ID,VALID_NAME)
        # Assert
        self.assertEqual(VALID_ID, testgroup.id)
        self.assertEqual(VALID_NAME, testgroup.name)

    def test_add_test_doNothing_ifTestIsNotFound(self):
        testgroup = TestGroup(VALID_ID, VALID_NAME)
        test=Test(UNVALID_ID,VALID_DESCRIPTION)
        testgroup.add_test(test)
        self.assertEqual(0,len(testgroup.tests))


class CommandFactoryShould(unittest.TestCase):
    def test_raisesErrorIf_commandNotValid(self):
        with self.assertRaises(ApplicationError):
            command = cmd_factory.create('fake_command')
            command.execute()
    def test_addTestGroupCmd_withValid_Arguments(self):
        command = cmd_factory.create(VALID_ADDTESTGROUP_COMMAND)
        expected = command.execute()
        self.assertIsInstance(command, AddTestGroupCommand)
        self.assertIsInstance(command.models_factory, ModelsFactory)
        self.assertIsInstance(command.app_data, ApplicationData)
        self.assertEqual(expected, 'Group #1 created')
    def test_addTestCmd_when_argumentsAreValid(self):
        first_command = cmd_factory.create(VALID_ADDTESTGROUP_COMMAND)
        second_command = cmd_factory.create(VALID_ADDTEST_COMMAND)
        first_command.execute()
        expected = second_command.execute()
        self.assertIsInstance(first_command.app_data, ApplicationData)
        self.assertIsInstance(first_command.models_factory, ModelsFactory)
        self.assertIsInstance(second_command.app_data, ApplicationData)
        self.assertIsInstance(second_command.models_factory, ModelsFactory)
        self.assertEqual(expected, 'Test #1 added to group #1')

    def test_addTestRunCmd_IfValid_Arguments(self):
        command = cmd_factory.create(VALID_ADDTESTRUN_COMMAND)
        test = Test(VALID_ID, VALID_DESCRIPTION)
        testrun=TestRun(VALID_RESULT,VALID_RUNTIME)
        expected=test.add_test_run(testrun)
        self.assertIsInstance(command, AddTestRunCommand)
        self.assertIsInstance(command.models_factory, ModelsFactory)
        self.assertIsInstance(command.app_data, ApplicationData)
        self.assertEqual(expected,command.execute())

    def test_RemoveGroupCmd_If_GroupFound(self):
        command = cmd_factory.create(VALID_REMOVEGROUP_COMMAND)
        testgroup = TestGroup(VALID_ID, VALID_NAME)
        expected=command.execute()
        self.assertEqual(expected,f'Group #{VALID_ID} removed')

    def test_RemoveGroupCmd_If_GroupNotFound(self):
        command = cmd_factory.create(VALID_REMOVEGROUP_COMMAND)
        expected=command.execute()
        self.assertEqual(expected,f'Group #{VALID_ID} not found')

class ModelsFactoryShould(unittest.TestCase):
    def testgroup_init_setProperties_correctly(self):
        group=TestGroup(VALID_ID,VALID_NAME)
        self.assertEqual(VALID_NAME,group.name)
        self.assertEqual(VALID_ID, group.id)
    def test_init_setProperties_correctly(self):
        test=Test(VALID_ID,VALID_DESCRIPTION)
        self.assertEqual(VALID_ID,test.id)
        self.assertEqual(VALID_DESCRIPTION, test.description)
    def testrun_init_setProperties_correctly(self):
        testrun=TestRun(VALID_RESULT,VALID_RUNTIME)
        self.assertEqual(VALID_RESULT,testrun.test_result)
        self.assertEqual(VALID_RUNTIME, testrun.runtime_ms)

class ApplicationData_Should(unittest.TestCase):
    def test_properties_returnCorrectTypes(self):
        # Arrange
        data = ApplicationData()

        # Act & Assert
        self.assertIsInstance(data.groups, tuple)

    def test_findTestGroup_returnsSuccessfully_whenCategoryExists(self):
        # Arrange
        models_factory=ModelsFactory
        data = ApplicationData()
        models_factory.create_group(VALID_NAME)

        # Act
        group = data.find_group(VALID_NAME)

        # Assert
        self.assertEqual(VALID_NAME, group.name)

    def test_findTestGroup_raisesError_whenCategoryDoesNotExist(self):
        # with self.assertRaises(None):
        #     data = ApplicationData()
        #     data.find_group(VALID_NAME)
        # Arrange
        models_factory = ModelsFactory
        data = ApplicationData()
        models_factory.create_group(VALID_NAME)
        FAKE_GROUP="non existing group"
        # Act
        group = data.find_group(FAKE_GROUP)

        # Assert
        self.assertEqual(None, group.name)
    def test_findTest_returnsSuccessfully_whenCategoryExists(self):
        # Arrange
        models_factory=ModelsFactory
        data = ApplicationData()
        models_factory.create_test(VALID_DESCRIPTION)

        # Act
        test = data.find_test(VALID_DESCRIPTION)

        # Assert
        self.assertEqual(VALID_DESCRIPTION, test.description)

    def test_findTest_raisesError_whenCategoryDoesNotExist(self):
        # with self.assertRaises(None):
        #     data = ApplicationData()
        #     data.find_group(VALID_NAME)
        # Arrange
        models_factory = ModelsFactory
        data = ApplicationData()
        models_factory.create_test(VALID_DESCRIPTION)
        FAKE_TEST="non existing test"
        # Act
        test = data.find_test(FAKE_TEST)

        # Assert
        self.assertEqual(None, test.description)

    def test_addGroup_addsGroup(self):
        # Arrange
        models_factory = ModelsFactory
        data = ApplicationData()
        group = models_factory.create_group(VALID_ID,VALID_NAME)


        # Act
        data.add_group(group)

        # Assert
        self.assertEqual(tuple([group]), data.groups)

    def test_addGroup_returnsFalse_ifGroupExist(self):
        # Arrange
        models_factory = ModelsFactory
        data = ApplicationData()
        group = models_factory.create_group(VALID_ID, VALID_NAME)

        # Act
        data.add_group(group)
        self.assertFalse(data.add_group(group))

    def test_RemoveGroupCmd_removesGroup(self):
        models_factory = ModelsFactory
        data = ApplicationData()
        group = models_factory.create_group(VALID_ID, VALID_NAME)
        data.remove_group(group)
        self.assertEqual(tuple(),data.find_group())

    def test_RemoveGroupCmd_returnsFalse_ifGroup_doesntExist(self):
        models_factory = ModelsFactory
        data = ApplicationData()
        group = models_factory.create_group(VALID_ID, VALID_NAME)
        data.remove_group(group)
        self.assertFalse(data.remove_group(group))




