input_str = input().split(',')

input_list = []
for i in input_str:
    input_list.append(i)

next_list = input_list
final_list = input_list
for x in range(len(final_list) - 3):
    for y in range(1, len(final_list) - 4):
        if next_list[x] == next_list[y]:
            final_list.pop(y)
        next_list = final_list

output=''
for i in final_list:
    output+=i+','
print(output.removesuffix(','))