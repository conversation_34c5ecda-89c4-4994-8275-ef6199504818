from commands.base.base_command import BaseCommand
from models.test import Test

class AddTest(BaseCommand):
    def execute(self):
        test_group_id, description = self._params
        test_group_id = int(test_group_id)
        new_test = Test(description)
        group = self._app_data.find_test_group(test_group_id)
        group.add_test(new_test)
        return f'Test #{new_test.id} added to group #{group.id}'