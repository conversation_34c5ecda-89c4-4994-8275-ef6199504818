from commands.base_command import BaseCommand
from models.test_run import TestRun
from core.application_data import ApplicationData

class AddTestRun(BaseCommand):
    counter_id = 0
    def __init__(self, params: list[str], app_data: ApplicationData):
        AddTestRun.counter_id += 1
        self._test_run_counter = AddTestRun.counter_id
        super().__init__(params, app_data)

    def execute(self):
        test_id = int(self._params[0])
        result = str(self._params[1])
        runtime_ms = int(self._params[2])
        test = self._app_data.find_test_by_id(test_id)
        new_test_run = TestRun(result, runtime_ms)
        test._test_runs.append(new_test_run)
        if test is None:
            return f"No test Added"
        else:
            return f"TestRun registered"
