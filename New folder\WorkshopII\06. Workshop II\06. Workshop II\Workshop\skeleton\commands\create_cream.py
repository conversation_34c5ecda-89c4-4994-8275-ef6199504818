from core.application_data import ApplicationData
from models.gender import Gender
from commands.validation_helpers import validate_params_count, try_parse_float
from models.product import Product
from models.usage_type import UsageType
from models.scent import Scent

class CreateCreamCommand:

    def __init__(self, params, app_data: ApplicationData):
        validate_params_count(params, 5)
        self._params = params
        self._app_data = app_data

    def execute(self):
        cream_name = self._params[0]
        cream_brand=self._params[1]
        cream_price=Product.is_price_valid(self,price=self._params[2])
        cream_gender=Gender.from_string(self._params[3])
        cream_scent=Scent.to_string(self._params[4])

        if self._app_data.product_exists(cream_name):
            raise ValueError(f'Cream with name {cream_name} already exists!')

        self._app_data.create_cream(cream_name,cream_brand,cream_price,cream_gender,cream_scent)


        return f'Cream with name {cream_name} was created!'
