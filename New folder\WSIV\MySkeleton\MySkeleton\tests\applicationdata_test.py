import unittest
from core.application_data import ApplicationData
from models.test_group import TestGroup
from models.test import Test

class ApplicationData_Should(unittest.TestCase):
    def setUp(self):
        self.app_data = ApplicationData()

    def test_add_group(self):
        group = TestGroup(1, 'Test Group 1')
        result = self.app_data.add_group(group)
        self.assertTrue(result)
        self.assertEqual(len(self.app_data.groups), 1)

    def test_add_group_with_existing_group(self):
        group = TestGroup(1, 'Test Group 1')
        self.app_data.add_group(group)
        result = self.app_data.add_group(group)
        self.assertFalse(result)
        self.assertEqual(len(self.app_data.groups), 1)

    def test_remove_group(self):
        group = TestGroup(1, 'Test Group 1')
        self.app_data.add_group(group)
        result = self.app_data.remove_group(1)
        self.assertTrue(result)
        self.assertEqual(len(self.app_data.groups), 0)

    def test_remove_group_with_non_existing_group(self):
        result = self.app_data.remove_group(1)
        self.assertFalse(result)
        self.assertEqual(len(self.app_data.groups), 0)

    def test_find_group(self):
        group = TestGroup(1, 'Test Group 1')
        self.app_data.add_group(group)
        result = self.app_data.find_group(1)
        self.assertEqual(result, group)

    def test_find_group_with_non_existing_group(self):
        result = self.app_data.find_group(1)
        self.assertIsNone(result)

    def test_find_test(self):
        group = TestGroup(1, 'Test Group 1')
        test = Test(1, 'Test 1')
        group.add_test(test)
        self.app_data.add_group(group)
        result = self.app_data.find_test(1)
        self.assertEqual(result, test)

    def test_find_test_with_non_existing_test(self):
        result = self.app_data.find_test(1)
        self.assertIsNone(result)