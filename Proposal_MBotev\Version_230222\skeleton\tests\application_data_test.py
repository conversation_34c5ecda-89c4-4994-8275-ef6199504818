import unittest
from core.application_data import ApplicationData
from core.pending_packages import PendingPackages
from core.truck_parking import TruckParking
from models.route import Route
from models.package import Package
from models.truck import Truck

VALID_START_LOCATION='SYD'
VALID_END_LOCATION='MEL'
VALID_WEIGHT=23.0
VALID_CONTACT_INFO='<PERSON>, 23, Silver route, Melbourne'

VALID_NAME='Scania'
VALID_CAPACITY=23000
VALID_MAX_RANGE=10000

EXPECTED_OUTPUT_NO_ROUTES='No routes available!'
EXPECTED_OUTPUT_YES_ROUTES='Route ID: 1 | BRI (Oct 10th 06:00h) -> SYD (Oct 10th 20:00h) -> MEL (Oct 11th 09:38h): #Package ID:1'

class ApplicationData_Should(unittest.TestCase):
    def test_properties_RetrunCorrectType(self):
        app_data=ApplicationData()

        self.assertIsInstance(app_data.routes,tuple)
        self.assertIsInstance(app_data.pending_packages,PendingPackages)
        self.assertIsInstance(app_data.truck_parking,TruckParking)

    def test_addroute_AddsRoute(self):
        app_data=ApplicationData()
        route1=Route('Oct 10th 06:00h', 'BRI->SYD->MEL')
        route2=Route('Oct 12th 06:00h', 'SYD->MEL->ADL')
        app_data.add_route(route1)
        app_data.add_route(route2)

        self.assertEqual(2,len(app_data.routes))

    def test_searchroute_returnsStrIfExist(self):
        app_data=ApplicationData()
        route1=Route('Oct 10th 06:00h', 'BRI->SYD->MEL')
        route2=Route('Oct 12th 06:00h', 'SYD->MEL->ADL')
        app_data.add_route(route1)
        app_data.add_route(route2)

        found_routes=app_data.search_route('SYD','MEL')

        self.assertIsInstance(found_routes,str)
        
    def test_searchroute_returnsNoneIfNoExists(self):
        app_data=ApplicationData()

        found_routes=app_data.search_route('SYD','MEL')

        self.assertIsNone(found_routes,None)

    def test_findroute_by_id_returnRouteIfExist(self):
        app_data=ApplicationData()
        route1=Route('Oct 10th 06:00h', 'BRI->SYD->MEL')
        route2=Route('Oct 12th 06:00h', 'SYD->MEL->ADL')
        app_data.add_route(route1)
        app_data.add_route(route2)

        found_id1=app_data.find_route(1)
        found_id2=app_data.find_route(2)

        self.assertIsInstance(found_id1,Route)
        self.assertEqual(found_id2.id, route2.id)

    def test_findroute_by_id_returnsNoneIfNoExist(self):
        app_data=ApplicationData()

        found_id1=app_data.find_route(1)

        self.assertIsNone(found_id1,None)

    def test_assignpackage_RaisesValueErrorIfRouteNoExist(self):
        app_data=ApplicationData()

        with self.assertRaises(ValueError):
            app_data.assign_package(1,1)

    def test_assignpackage_AddPAckageToRoute_RaisesErrorIfNoTrcuk(self):
        app_data=ApplicationData()
        route1=Route('Oct 10th 06:00h', 'BRI->SYD->MEL')
        app_data.add_route(route1)
        package1=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)
        app_data.pending_packages.create_package(package1)
        
        with self.assertRaises(ValueError):
            app_data.assign_package(1,1)

        self.assertEqual(1,len(route1.packages))

    def test_assigntruck_AddTruckToRouteAndRaiseErrorIfCapacityNotEnough(self):
        app_data=ApplicationData()
        route1=Route('Oct 10th 06:00h', 'BRI->SYD->MEL')
        app_data.add_route(route1)
        package1=Package(VALID_START_LOCATION,VALID_END_LOCATION,50000,VALID_CONTACT_INFO)
        app_data.pending_packages.create_package(package1)

        with self.assertRaises(ValueError):
            app_data.assign_package(1,1)

        truck1=Truck(VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)
        app_data.truck_parking.create_truck(truck1)

        with self.assertRaises(ValueError):
            app_data.assign_truck(1,VALID_NAME)

        self.assertEqual(1,len(route1.trucks))


    def test_routecontaningpackage_FindRouteWithPackageId(self):
        app_data=ApplicationData()
        route1=Route('Oct 10th 06:00h', 'BRI->SYD->MEL')
        app_data.add_route(route1)
        truck1=Truck(VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)
        app_data.truck_parking.create_truck(truck1)
        app_data.assign_truck(1,VALID_NAME)
        package1=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)
        app_data.pending_packages.create_package(package1)
        app_data.assign_package(1,1)

        route_found=app_data.route_containg_package(1)

        self.assertEqual(route1,route_found)

    def test_inforoutes_correctStringIfNoRoutes(self):
        app_data=ApplicationData()

        self.assertEqual(EXPECTED_OUTPUT_NO_ROUTES,app_data.info_routes())

    def test_inforoutes_correctStringIfRouteExists(self):
        app_data=ApplicationData()
        route1=Route('Oct 10th 06:00h', 'BRI->SYD->MEL')
        app_data.add_route(route1)
        truck1=Truck(VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)
        app_data.truck_parking.create_truck(truck1)
        app_data.assign_truck(1,VALID_NAME)
        package1=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)
        app_data.pending_packages.create_package(package1)
        app_data.assign_package(1,1)

        self.assertEqual(EXPECTED_OUTPUT_YES_ROUTES,app_data.info_routes())

    def test_availableRouteOnDate_CorrectStringIfNoRoute(self):
        app_data=ApplicationData()
        route1=Route('Oct 10th 06:00h', 'BRI->SYD->MEL')
        app_data.add_route(route1)

        txt=app_data.available_routes_on_date('Oct 10th 05:00h')

        self.assertEqual('No routes available!',txt)

    def test_availableRouteOnDate_CorrectStringIfYesRoute(self):
        app_data=ApplicationData()
        route1=Route('Oct 10th 06:00h', 'BRI->SYD->MEL')
        app_data.add_route(route1)

        txt=app_data.available_routes_on_date('Oct 10th 07:00h')
        expected_txt='Route ID: 1 | BRI (Oct 10th 06:00h) -> SYD (Oct 10th 20:00h) -> MEL (Oct 11th 09:38h):\n Delivery weight: 0kg.  Next stop-> SYD in 13h00min.'

        self.assertEqual(expected_txt,txt)
















    


    










