from models.product import Product
from models.gender import Gender

class Shampoo(Product):
    def __init__(self, name, brand, price, gender, usage_type, milliliters):
        # if len(name) < 3 or len(name) > 10:
        #     raise ValueError('')
        # if len(brand) < 2 or len(brand) > 10:
        #     raise ValueError('')
        # if price < 0:
        #     raise ValueError('')
        # if milliliters < 0:
        #     raise ValueError('')
        # if not (usage_type == 'Every_Day' or usage_type == 'Medical'):
        #     raise ValueError('')

    #     self._name = name
    #     self._brand = brand
    #     self._price = price
    #     self._gender = Gender(gender)
        super().__init__(name, brand, price, gender)
        self.usage_type = usage_type
        self.milliliters = milliliters

    @property
    def milliliters(self):
        return self._milliliters
    
    @milliliters.setter
    def milliliters(self, value):
        if value < 0:
            raise ValueError('')

        self._milliliters = value

    @property
    def usage_type(self):
        return self._usage_type
    
    @usage_type.setter
    def usage_type(self, value: str):
        if not (value == 'Every_Day' or value == 'Medical'):
            raise ValueError('')

        self._usage_type = value

    def to_string(self):
        return '\n'.join([
            f' #{self.name} {self.brand}',
            f' #Price: ${self.price:.2f}',
            f' #Gender: {self.gender}',
            f' #Milliliters: {self.milliliters}',
            f' #Usage: {self._usage_type}'
        ])