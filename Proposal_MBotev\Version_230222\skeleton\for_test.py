
from core.application_data import ApplicationData
from models.route import Route
from models.package import Package
# from models.actros import Actros
from core.truck_generator import TruckGenerator
# from models.pending_packages import PendingPackages

app_data = ApplicationData()
tr=TruckGenerator(app_data).execute()
# first_route = Route('Oct 10th 06:00h', 'BRI->SYD->MEL')
# app_data.add_route(first_route)

# actros=Actros(1026,'Actros')
# app_data.truck_parking.create_truck(actros)
print(app_data.truck_parking)
print(tr)
# print(first_route.trucks)
# app_data.add_route(first_route)
# app_data.assign_truck(1,'Actros')
# print(first_route.trucks)
# print(app_data.truck_parking.find_truck('Actros'))
# app_data.truck_parking.remove_truck(actros)
# print(app_data.truck_parking)
# second_route = Route('Oct 12th 06:00h', 'SYD->MEL->ADL')
# app_data.add_route(second_route)
# third_route=Route('Sep 12th 06:00h','ASP->ADL->MEL->SYD->BRI')
# app_data.add_route(third_route)
# print(app_data.search_route('SYD','MEL'))

# print(app_data.find_route(3))

# first_package=Package('SYD','MEL',45.0,'John Smith, 1,Kings road, Melborne')

# app_data.pending_packages.create_package(first_package)
# print(app_data.pending_packages)
# print(app_data.info_routes())
# app_data.assign_package(1,1)
# print(app_data.pending_packages)
# print(app_data.info_routes())
# print(first_route.packages[0])
# print(first_route.current_route_weight)
      
# second_package=Package('ASP','BRI',23000.0,'Jeremy Inc., 23,Salt lake road, Brisbane')
# app_data.pending_packages.create_package(second_package)
# # app_data.assign_package(3,2)
# # # print(second_package)
# # print(third_route.packages[0])
# # print(third_route.current_route_weight)
# print(app_data.pending_packages)

# print(first_route.current_situation(1,'Oct 11th 20:00h'))