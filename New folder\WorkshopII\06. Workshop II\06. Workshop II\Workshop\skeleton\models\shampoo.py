from models.product import Product
from models.usage_type import UsageType

class Shampoo(Product):
    def __init__(self, name, brand, price, gender, usage_type, milliliters):
        super().__init__(name,brand,price,gender)
        self._usage_type=UsageType.from_string(usage_type)
        self.milliliters=self.are_milliliters_negative(milliliters)

    @property
    def milliliters(self):
        return self._milliliters

    @milliliters.setter
    def milliliters(self,value):
        self._milliliters=self.are_milliliters_negative(value)


    @property
    def usage_type(self):
        return self._usage_type

    def to_string(self):

        return '\n'.join([
            f' #{self.name} {self.brand}',
            f' #Price: ${self.price:.2f}',
            f' #Gender: {self.gender}',
            f' #Milliliters: {self.milliliters}',
            f' #Usage: {self.usage_type}'
        ])



    


    def are_milliliters_negative(self,value):
        if isinstance(int(value),int):
            if int(value)<0:
                raise ValueError('Milliliters cannot be negative !')
            return int(value)
        else:
            raise ValueError('Invalid milliliters !')

    
    


