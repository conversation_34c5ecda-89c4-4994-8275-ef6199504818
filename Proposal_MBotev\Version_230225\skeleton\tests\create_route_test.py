import unittest
from unittest.mock import MagicMock
from commands.create_route import C<PERSON><PERSON>oute<PERSON>ommand
from core.application_data import ApplicationData
from models.route import Route


class TestCreateRouteCommand(unittest.TestCase):

    def test_create_route_command_with_valid_params(self):
        app_data = ApplicationData()
        create_route_command = CreateRouteCommand(['Oct 10th 06:00h', 'BRI->SYD->MEL'], app_data)
        result = create_route_command.execute()
        expected_result = f'Route with ID: 1, Departure Time: Oct 10th 06:00h, Locations: BRI->SYD->MEL, Distance: 930 km, Weight Capacity: 50.0 kg, Max Range: 1200 km was created!'
        self.assertEqual(result, expected_result)

    def test_create_route_command_with_invalid_params(self):
        app_data = ApplicationData()
        create_route_command = CreateRouteCommand(['invalid', 'param'], app_data)
        with self.assertRaises(ValueError):
            create_route_command.execute()

    def test_create_route_command_adds_route_to_app_data(self):
        app_data = ApplicationData()
        app_data.add_route = MagicMock()
        create_route_command = CreateRouteCommand(['Oct 10th 06:00h', 'BRI', 'SYD', 'MEL'], app_data)
        create_route_command.execute()
        app_data.add_route.assert_called_once_with(Route(1, 'Oct 10th 06:00h', 'BRI->SYD->MEL'))

if __name__ == '__main__':
    unittest.main()
