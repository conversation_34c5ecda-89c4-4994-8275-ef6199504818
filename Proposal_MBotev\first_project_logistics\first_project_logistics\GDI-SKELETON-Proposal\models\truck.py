from models.route import Route
from models.package import Package
from core.application_data import ApplicationData


class Truck:
    def __init__(self, id: int, name: str, capacity: int, max_range: int, num_of_vehicles: int) -> None:
        self._id = id
        self._name = name
        self._capacity = capacity
        self._max_range = max_range
        self._num_of_vehicles = num_of_vehicles
        self._delivery_routes: list[Route] = []
        self._delivery_packages: list[Package] = []
    
    @property
    def id(self):
        return self._id
    
    @property
    def name(self):
        return self._name

    @property
    def capacity(self):
        return self._capacity

    @property
    def max_range(self):
        return self._max_range

    @property
    def num_of_vehicles(self):
        return self._num_of_vehicles

    @property
    def delivery_routes(self):
        return tuple(self._delivery_routes)

    @property
    def delivery_packages(self):
        return tuple(self._delivery_packages)

    def assign_route(self, delivery_route: Route)->None:
        if delivery_route.total_distance > self._max_range:
            raise ValueError("The delivery route is too far for this truck")
        self._delivery_routes.append(delivery_route)

    def assign_package(self, delivery_package: Package):
        if delivery_package.weight > self._capacity:
            raise ValueError("The delivery package is too heavy for this truck")
        self._delivery_packages.append(delivery_package)
        
    def add_route(self, delivery_route: Route) -> None:
        if delivery_route.total_distance > self._max_range:
            raise ValueError("The delivery route is too far for this truck")
        self._delivery_routes.append(delivery_route)
        delivery_route.add_truck(self)

    def add_package(self, delivery_package: Package) -> None:
        if delivery_package.weight > self._capacity:
            raise ValueError("The delivery package is too heavy for this truck")
        self._delivery_packages.append(delivery_package)
        delivery_package.add_truck(self)

    def __str__(self) -> str:
        return '\n'.join([
                f'Truck ID: {self.id}',
                f'Name: {self._name}',
                f'Capacity: {self._capacity}',
                f'Max range: {self._max_range}',,
        ])

    # def get_trucks_info(self, app_data: ApplicationData):
    #     pass
    #     # params = [str(self.id)]
    #     # view_truck_command = ViewTruckCommand(params, app_data)
    #     # return view_truck_command.execute()
