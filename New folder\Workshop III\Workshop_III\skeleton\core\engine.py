
from core.command_factory import CommandFactory


class Engine:
    def __init__(self, factory: CommandFactory):
        self._command_factory = factory

    def start(self):
        results = []
        while True:
            command_line = input()
            if command_line.lower() == 'end':
                break

            command = self._command_factory.create(command_line)
            results.append(command.execute())

        print('\n'.join(results))


        

