
from src.linked_list_node import LinkedListNode


class CustomQueue:
    def __init__(self):
        self._head = None
        self._tail = None
        self._count = 0

    def enqueue(self, value):
        node = LinkedListNode(value)
        if self._tail is None:
            self._head = node
        else:
            self._tail.next = node
        self._tail = node
        self._count += 1

    def dequeue(self):
        if self.is_empty:
            raise ValueError("Queue is empty.")
        value = self._head.value
        self._head = self._head.next
        if self._head is None:
            self._tail = None
        self._count -= 1
        return value

    @property
    def count(self):
        return self._count

    @property
    def is_empty(self):
        return self._head is None

    def peek(self):
        if self.is_empty:
            raise ValueError("Queue is empty.")
        return self._head.value
