from models.route import Route
from core.pending_packages import PendingPackages
from core.truck_parking import TruckParking
from datetime import datetime


class ApplicationData:
    def __init__(self):
        self._routes: list[Route] = []
        self._pending_packages=PendingPackages()
        self._truck_parking=TruckParking()
        

    @property
    def routes(self):
        return tuple(self._routes)
    
    @property
    def pending_packages(self):
        return self._pending_packages
    
    @property
    def truck_parking(self):
        return self._truck_parking

    def search_route(self, start, end) -> str | None:
        founded_routes = []
        for route in self._routes:
            if start in route.locations:
                if end in route.locations:
                    if route.locations.index(start) < route.locations.index(end):
                        founded_routes.append(route)
        if founded_routes==[]:
            return None
        return '\n'.join(f'{route}' for route in founded_routes)

    def add_route(self, route: Route) -> None:
        self._routes.append(route)

    

    def find_route(self,route_id: int)->Route | None:
        founded=[route for route in self._routes if route.id == route_id]
        if founded == []:
            return None
        return founded[0]
    
    
    
    def assign_package(self,route_id: int, package_id: int)->None | ValueError:
        found_package=self._pending_packages.find_package(package_id)
        if self.find_route(route_id) == None:
            raise ValueError('No route found!')
        for r in self._routes:
            if r.id == route_id:
                r._packages.append(found_package)
                self._pending_packages.remove_package(found_package)
                # verify sufficient route weight capacity
                if r.current_route_weight>r.weight_capacity:
                    raise ValueError(f'Package with ID: {package_id}  was assigned to Route with ID: {route_id}! /!\The maximum weight capacity of this route is attained.Please, add a truck! You need {r.current_route_weight-r.weight_capacity}kg more!')

        
    def assign_truck(self,route_id: int,truck_name: str)->None | ValueError:
        found_truck=self._truck_parking.find_truck(truck_name)
        if self.find_route(route_id) == None:
            raise ValueError('No route found!') 
        for r in self._routes:
            if r.id == route_id:
                r._trucks.append(found_truck)
                self._truck_parking.remove_truck(found_truck)
                #verify sufficient truck weight capacity
                if r.current_route_weight>found_truck.capacity:
                    raise ValueError(f'Truck:{truck_name} was assigned to Route with ID: {route_id}! /!\The capacity of this truck is not sufficient. You need {r.current_route_weight-found_truck.capacity}kg more!/!\ ')
                #verify sufficient truck max range
                if r.total_distance>found_truck.max_range:
                    raise ValueError(f'Truck:{truck_name} was assigned to Route with ID: {route_id}! /!\The max range of this truck is not sufficient. You need {r.total_distance-found_truck.max_range}km more!/!\ ')

    def route_containg_package(self,package_id: int)->Route:
        for route in self._routes:
            package=route.find_package_in_route(package_id)
            if package in route.packages:
                return route


    def info_routes(self)->str:
        if self._routes==[]:
            return f'No routes available!'
            
        return '\n'.join(f'{route}: {route.info_packages_in_route()}' for route in self._routes)

    def available_routes_on_date(self,request_time: str)->str:
        r_time=datetime.strptime(request_time, Route.FORMAT)
        available_routes=[]
        
        for route in self._routes:
            if route.times()[-1]<r_time or route.times()[0]>r_time:
                continue
            available_routes.append(route)
            
        
        if available_routes==[]:
            return f'No routes available!'
        info='\n'.join(f'{route}:\n Delivery weight: {route.current_route_weight}kg. {route.next_stop(request_time)}' for route in available_routes)
        
        return f'{info}'


    



    

    