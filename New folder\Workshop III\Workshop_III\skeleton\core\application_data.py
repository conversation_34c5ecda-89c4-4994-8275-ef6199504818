
from models.test import Test
from models.test_group import TestGroup


class ApplicationData:
    def __init__(self):
        self._test_groups: list[TestGroup] = []

    @property
    def groups(self):
        return tuple(self._test_groups)

    def add_test_group(self, test_group: TestGroup):
        if test_group in self.groups:
            raise ValueError(f'Testgroup {test_group.name} already exists')
        self._test_groups.append(test_group)

    def find_test_group(self, id: int) -> TestGroup:
        for group in self._test_groups:
            if group.id == id:
                return group
        raise ValueError(f'No test group found for ID {id}')

    def remove_test_group(self, id: int):
        for group in self._test_groups:
            if group.id == id:
                self._test_groups.remove(group)
                break
        
    def find_test(self, id: int) -> Test:
        for group in self._test_groups:
            for test in group.tests:
                if test.id == id:
                    return test
        
    
