import unittest
from unittest.mock import Mock
from datetime import datetime, timedelta
from commands.create_package import CreatePackageCommand
from core.application_data import ApplicationData
from models.package import Package
from models.truck import Truck

class TestCreatePackageCommand(unittest.TestCase):

    def test_create_package_success(self):
        # Arrange
        app_data = ApplicationData()
        params = ["SYD", "MEL", "45.0", "John Doe", "<EMAIL>"]
        expected_output = "Package with ID: 1 to End location:Melbourne was created!"
        departure_time = datetime.strptime("Oct 8th 06:00h", "%b %dth %H:%Mh")  ### 
        command = CreatePackageCommand(params, app_data, departure_time)         #command = CreatePackageCommand(params, app_data)Prev
        app_data.add_truck(Truck('ABC123', 5000, 87, 2500)) # Add a truck to the fleet for the route
    
        # Act
        result = command.execute()

        # Assert
        self.assertEqual(result, expected_output)
        self.assertEqual(len(app_data.pending_packages.get_packages()), 1)
        package = app_data.pending_packages.get_packages()[0]
        self.assertIsInstance(package, Package)
        self.assertEqual(package.start_location, "SYD")
        self.assertEqual(package.end_location, "MEL")
        self.assertEqual(package.weight, 45.0)
        self.assertEqual(package.contact_info, ("John Doe", "<EMAIL>"))

        # Check for suitable delivery route and select the first one
        routes = app_data.available_routes_on_date("Oct 8th")
        self.assertEqual(len(routes), 2)
        route = routes[0]
        expected_route_str = "Route with ID: 1, Departure Time: Oct 10th 06:00h, Locations: BRI->SYD->MEL, Distance: 930 km, Weight Capacity: 50.0 kg, Max Range: 1200 km"
        self.assertEqual(str(route), expected_route_str)

        # Add the package to the selected route
        app_data.add_package_to_route(package, route.id)

        # Check that package was added to the route and its expected delivery time was updated
        expected_package_str = "Package with ID: 1, Start Location: Sydney, End Location: Melbourne, Weight: 45.0 kg, Contact Info: ('John Doe', '<EMAIL>'), Current Location: Brisbane, Current ETA: Oct 10th 20:00h"
        self.assertEqual(str(package), expected_package_str)

    def test_create_package_missing_params(self):
        # Arrange
        app_data = ApplicationData()
        params = ["ASP", "", "23000.0", "John Doe", "<EMAIL>"]
        command = CreatePackageCommand(params, app_data)

        # Act
        with self.assertRaises(ValueError):
            command.execute()
            self.assertEqual(len(app_data.pending_packages.packages), 0)