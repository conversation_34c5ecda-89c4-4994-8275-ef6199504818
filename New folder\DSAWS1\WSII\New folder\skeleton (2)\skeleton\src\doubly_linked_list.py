from src.linked_list_node import LinkedListNode


class DoublyLinkedList:
    def __init__(self):
        self._head: LinkedListNode = None
        self._tail: LinkedListNode = None
        self._count = 0

    @property
    def count(self):
        return self._count

    @property
    def head(self):
        return self._head

    @property
    def tail(self):
        return self._tail

    def add_first(self, value):
        self._insert_before_head(value)

        self._count += 1

    def add_last(self, value):
        self._insert_after_tail(value)

        self._count += 1

    def insert_after(self, node: LinkedListNode, value):
        if node is None:
            raise ValueError('Node is None!!')
        next = node.next
        if next is not None:
            node.next = LinkedListNode(value)
            node.next.prev = node
            next.prev = node.next
            node.next.next = next
        else:
            self._insert_after_tail(value)

        self._count += 1

    def insert_before(self, node: LinkedListNode, value):
        if node is None:
            raise ValueError('Node is None!!')
        prev = node.prev
        if prev is not None:
            node.prev = LinkedListNode(value)
            prev.next = node.prev
            node.prev.next = node
            node.prev.prev = prev
        else:
            self._insert_before_head(value)

        self._count += 1

    def remove_first(self):
        first = self._head
        if first is None:
            raise ValueError('DLL is empty')
        if self._count == 0 or self._count == 1:
            self._head = None
            self._tail = None

        else:
            self._head = self._head.next
            first.prev = None
        self._count -= 1
        return first.value

    def remove_last(self):
        last = self._tail
        if last is None:
            raise ValueError('DLL is empty')
        if self._count == 0 or self._count == 1:
            self._head = None
            self._tail = None

        else:
            self._tail = self._tail.prev
            last.next = None
        self._count -= 1
        return last.value

    def find(self, value):
        node = self._head
        while node is not None:
            if node.value == value:
                return node
            node = node.next
        return None

    def values(self):
        values = []
        node = self._head
        while node is not None:
            values.append(node.value)
            node = node.next
        return tuple(values)

    def _insert_before_head(self, value):
        node = LinkedListNode(value)
        if self._head is None:
            self._head = node
            self._tail = node

        else:
            head = self._head
            self._head = LinkedListNode(value)
            self._head.next = head

    def _insert_after_tail(self, value):
        node = LinkedListNode(value)
        if self._tail is None:
            self._tail = node
            self._head = node

        else:
            self._tail.next = LinkedListNode(value)
            self._tail.next.prev = self.tail
            self._tail = self._tail.next
