from commands.validations import try_parse_int
from commands.base.base_command import BaseCommand
from core.application_data import ApplicationData
from models.test import Test
from models.test_run import TestRun


class CommandFactory:
    def __init__(self, data: ApplicationData):
        self._app_data = data

    def create(self, input_line):
        cmd, *params = input_line.split()

        if cmd.lower() == "addtestgroup":
            return AddTestGroup(params, self._app_data)
        if cmd.lower() == "addtest":
            return AddTest(params, self._app_data)
        if cmd.lower() == "addtestrun":
            return AddTestRun(params, self._app_data)
        if cmd.lower() == "removegroup":
            return RemoveGroup(params, self._app_data)
        if cmd.lower() == "testreport":
            return TestReport(params, self._app_data)
        if cmd.lower() == "viewgroup":
            return ViewGroup(params, self._app_data)
        if cmd.lower() == "viewsystem":
            return ViewSystem(params, self._app_data)

        raise ValueError(f"Invalid command name: {cmd}!")


class AddTestGroup(BaseCommand):
    new_group_id = 0

    def __init__(self, params, app_data: ApplicationData):
        super().__init__(params, app_data)
        AddTestGroup.new_group_id += 1

    def execute(self):
        group_name = self._params[0]
        group_id = AddTestGroup.new_group_id
        self._app_data.add_group(group_id, group_name)

        return f"Group #{group_id} created"


class AddTest(BaseCommand):
    test_id = 0

    # fix counter for every new group counter resets

    def __init__(self, params, app_data: ApplicationData):
        super().__init__(params, app_data)
        AddTest.test_id += 1

    def execute(self):
        belongs_to_group = try_parse_int(self._params[0])
        test_description = self._params[1]

        all_groups = self._app_data.test_groups
        for group in all_groups:
            if group.id == belongs_to_group:
                new_test = Test(belongs_to_group, test_description)
                group.add_test(new_test)
        return f"Test #{AddTest.test_id} added to group #{belongs_to_group}"


class AddTestRun(BaseCommand):
    def __init__(self, params, app_data: ApplicationData):
        super().__init__(params, app_data)

    def execute(self):
        add_to_test_id = try_parse_int(self._params[0])
        test_result = self._params[1]
        runtime_ms = try_parse_int(self._params[2])

        all_groups = self._app_data.test_groups
        for group in all_groups:
            for i, test in enumerate(group.tests):
                if add_to_test_id == i + 1:
                    test_run = TestRun(test_result, runtime_ms)
                    test.add_test_run(test_run)

        return f"TestRun registered"


class RemoveGroup(BaseCommand):
    def __init__(self, params, app_data: ApplicationData):
        super().__init__(params, app_data)

    def execute(self):
        test_group_id = try_parse_int(self._params[0])

        all_groups = self._app_data.test_groups
        for index, group in enumerate(all_groups):
            if group.id == test_group_id:
                all_groups.pop(index)
        return f"Group #{test_group_id} removed"


class TestReport(BaseCommand):
    def __init__(self, params, app_data: ApplicationData):
        super().__init__(params, app_data)

    def execute(self):
        test_id_to_check = try_parse_int(self._params[0])

        passing_runs_count = 0
        failing_runs_count = 0
        total_runtime = 0

        all_groups = self._app_data.test_groups
        for group in all_groups:
            for i, test in enumerate(group.tests):
                if test_id_to_check == i + 1:
                    description = test.description
                    for test_run in test.test_runs:
                        if test_run.test_result == "pass":
                            passing_runs_count += 1
                        elif test_run.test_result == "fail":
                            failing_runs_count += 1
                        total_runtime += test_run.runtime_ms

        test_description = description
        test_runs_count = passing_runs_count + failing_runs_count
        avg_runtime = total_runtime / test_runs_count

        return f"#{test_id_to_check}. [{test_description}]: {test_runs_count} runs\n" \
               f"- Passing: {passing_runs_count}\n" \
               f"- Failing: {failing_runs_count}\n" \
               f"- Total runtime: {total_runtime}ms\n" \
               f"- Average runtime: {avg_runtime:.1f}ms"


class ViewGroup(BaseCommand):
    def __init__(self, params, app_data: ApplicationData):
        super().__init__(params, app_data)

    def execute(self):
        test_group_id = try_parse_int(self._params[0])

        to_string = []

        for group in self.app_data.test_groups:
            if group.id == test_group_id:
                group_name = group.name
                tests_count = len([x for x in group.tests])
                first_line = f"#{test_group_id}. {group_name} ({tests_count} tests)"
                to_string.append(first_line)

                for i, test in enumerate(group.tests):
                    test_description = test.description
                    tests_count = len(test.test_runs)
                    second_line = f"  #{i + 1}. [{test_description}]: {tests_count} runs"
                    to_string.append(second_line)

        return "\n".join(to_string)


class ViewSystem(BaseCommand):
    def __init__(self, params, app_data: ApplicationData):
        super().__init__(params, app_data)

    def execute(self):
        groups = self.app_data.test_groups
        test_groups_count = len(groups)
        output = []
        first_line = f"Test Reporter System ({test_groups_count} test groups)"
        output.append(first_line)
        for group in groups:
            group_id_num = group.id
            group_name = group.name
            tests_count = len(group.tests)
            second_line = f"  #{group_id_num}. {group_name} ({tests_count} tests)"
            output.append(second_line)

        return "\n".join(output)
