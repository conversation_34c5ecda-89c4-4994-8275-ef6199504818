from models.truck import Truck

class Actros(Truck):

    # COUNTER=1026
    def __init__(self, id: int,name: str, capacity=26000, max_range=13000):
        super().__init__(id, name, capacity, max_range)
        # id = Actros.COUNTER
        # Actros.COUNTER+=1
        if id< 1026:
            raise ValueError('The Actros id cannot be less than 1026')


        if id > 1040:
            raise ValueError('The Actros id cannot be more than 1040')
        # self._name='Actros'
        # self._capacity=26000
        # self._max_range=13000