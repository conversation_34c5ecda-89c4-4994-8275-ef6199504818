
from core.application_data import ApplicationData
from core.command_factory import CommandFactory
from core.engine import Engine
from core.truck_generator import TruckGenerator
# from export_data import export_routes_to_csv, export_packages_to_csv  #GDI Export function 

app_data = ApplicationData()
TruckGenerator(app_data).execute()

# Add routes and packages to app_data
# export_routes_to_csv('routes.csv', app_data.routes)     #GDI Export function 
# export_packages_to_csv('packages.csv', app_data.pending_packages.packages)      #GDI Export function 

cmd_factory = CommandFactory(app_data)
engine = Engine(cmd_factory)

engine.start()






"""

-------------------------------------------------------------------------------
Use case #1
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreateRoute Oct 12th 06:00h SYD->MEL->ADL
CreatePackage SYD MEL 45.0 <PERSON>, 1,Kings road, Melborne
SearchRoute SYD MEL
AssignPackageToRoute 1 1
ViewPackageInRoute 1 Oct 8th 10:38h
end

Use case #2
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreateRoute Oct 12th 06:00h SYD->MEL->ADL
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
AssignPackageToRoute 1 1
CreatePackage ASP BRI 23000.0 Jeremy Inc., 23,Salt lake road, Brisbane
CreateRoute Oct 12th 06:00h ASP->ADL->MEL->SYD->BRI
AssignPackageToRoute 3 2
ViewPackageInRoute 2 Oct 11th 10:38h
end

Use case #3
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreateRoute Oct 12th 06:00h SYD->MEL->ADL
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
AssignPackageToRoute 1 1
CreatePackage ASP BRI 23000.0 Jeremy Inc., 23,Salt lake road, Brisbane
CreateRoute Sep 12th 06:00h ASP->ADL->MEL->SYD->BRI
AssignPackageToRoute 3 2
ViewRoutes Sep 13th 06:38h
end

Use case #4-A
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
CreatePackage ASP BRI 23000.0 Jeremy Inc., 23,Salt lake road, Brisbane
ViewPendingPackages
end

Use case #4-B
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
CreatePackage ASP BRI 23000.0 Jeremy Inc., 23,Salt lake road, Brisbane
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreateRoute Oct 12th 06:00h SYD->MEL->ADL
CreateRoute Sep 12th 06:00h ASP->ADL->MEL->SYD->BRI
AssignPackageToRoute 1 1
AssignPackageToRoute 3 2
ViewPendingPackages
end

Use case #5
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
AssignPackageToRoute 1 1
ViewPackageInRoute 1 Oct 11th 09:38h
end

Use Case #6
Viewtrucks
end

Use Case #7

CreateRoute Oct 10th 06:00h BRI->SYD->MEL
Viewtrucks
Assigntrucktoroute 1 Actros
Viewtrucks
end
===================

CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreateRoute Oct 12th 06:00h SYD->MEL->ADL
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
SearchRoute SYD MEL
AssignPackageToRoute 1 1
ViewPackageInRoute 1 Oct 8th 10:38h
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreateRoute Oct 12th 06:00h SYD->MEL->ADL
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
AssignPackageToRoute 1 1
CreatePackage ASP BRI 23000.0 Jeremy Inc., 23,Salt lake road, Brisbane
CreateRoute Oct 12th 06:00h ASP->ADL->MEL->SYD->BRI
AssignPackageToRoute 3 2
ViewPackageInRoute 2 Oct 11th 10:38h
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreateRoute Oct 12th 06:00h SYD->MEL->ADL
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
AssignPackageToRoute 1 1
CreatePackage ASP BRI 23000.0 Jeremy Inc., 23,Salt lake road, Brisbane
CreateRoute Sep 12th 06:00h ASP->ADL->MEL->SYD->BRI
AssignPackageToRoute 3 2
ViewRoutes Sep 13th 06:38h
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
CreatePackage ASP BRI 23000.0 Jeremy Inc., 23,Salt lake road, Brisbane
ViewPendingPackages
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
CreatePackage ASP BRI 23000.0 Jeremy Inc., 23,Salt lake road, Brisbane
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreateRoute Oct 12th 06:00h SYD->MEL->ADL
CreateRoute Sep 12th 06:00h ASP->ADL->MEL->SYD->BRI
AssignPackageToRoute 1 1
AssignPackageToRoute 3 2
ViewPendingPackages
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
AssignPackageToRoute 1 1
ViewPackageInRoute 1 Oct 11th 09:38h
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
Assigntrucktoroute 1 Actros
end


=====================
"""
