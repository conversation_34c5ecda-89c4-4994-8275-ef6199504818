from models.constants.test_result import TestResult
from models.test_run import TestRun


class Test:
    id_counter = 1

    def __init__(self, description: str):
        self._id = Test.id_counter
        Test.id_counter += 1
        self._description = description
        self._test_runs: list[TestRun] = []

    @property
    def id(self):
        return self._id

    @property
    def description(self):
        return self._description

    @property
    def test_runs(self):
        return tuple(self._test_runs)

    def add_test_run(self, test_run: TestRun):
        self._test_runs.append(test_run)
