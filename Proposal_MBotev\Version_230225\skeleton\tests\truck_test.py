import unittest
from models.truck import Truck

VALID_TRUCK_ID=1001
VALID_TRUCK2_ID=1002

VALID_NAME='Scania'
VALID_CAPACITY=23000
VALID_MAX_RANGE=10000
EXPECTED_OUTPUT='Truck ID: 1001 | Name: Scania | Capacity: 23000 | Max Range: 10000'



class TruckShould(unittest.TestCase):
    def test_initializerSetsProperties(self):
        #Arrange & Act
        truck=Truck(VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)
        #Assert
        self.assertEqual(VALID_TRUCK_ID,truck.id)
        self.assertEqual(VALID_NAME,truck.name)
        self.assertEqual(VALID_CAPACITY,truck.capacity)
        self.assertEqual(VALID_MAX_RANGE,truck.max_range)

    def test_initializer_encreaseID(self):
        truck1=Truck(VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)
        truck2=Truck(VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)

        self.assertEqual(VALID_TRUCK2_ID,truck2.id)


    def test_str_returnsCorrectly(self):
        truck=Truck(VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)
        self.assertEqual(EXPECTED_OUTPUT,str(truck))
        
        
        
# import unittest
# from models.truck import Truck

# class TestTruck(unittest.TestCase):

#     def test_truck_creation(self):
#         truck = Truck("Actros", 50000, 9000)
#         self.assertEqual(truck.name, "Actros")
#         self.assertEqual(truck.capacity, 50000)
#         self.assertEqual(truck.max_range, 9000)

#     def test_truck_id(self):
#         truck1 = Truck("Actros", 50000, 9000)
#         truck2 = Truck("Scania", 42000, 8000)
#         self.assertNotEqual(truck1.id, truck2.id)

#     def test_truck_string_representation(self):
#         truck = Truck("Actros", 50000, 9000)
#         truck_str = str(truck)
#         self.assertTrue("Truck ID: " in truck_str)
#         self.assertTrue("Name: Actros" in truck_str)
#         self.assertTrue("Capacity: 50000" in truck_str)
#         self.assertTrue("Max Range: 9000" in truck_str)
