from core.application_data import ApplicationData

class ViewInformationCommand:
    def __init__(self, params, app_data: ApplicationData):
        self._params = params
        self._app_data = app_data

    def execute(self):
        info_type = self._params[0] if self._params else 'all'
        if info_type == 'routes':
            return self._app_data.get_routes_info()
        elif info_type == 'trucks':
            return self._app_data.get_trucks_info()
        elif info_type == 'packages':
            return self._app_data.get_packages_info()
        elif info_type == 'all':
            return self._app_data.get_routes_info() + '\n\n' + self._app_data.get_trucks_info() + '\n\n' + self._app_data.get_packages_info()
        else:
            raise ValueError(f"Invalid information type: {info_type}. Must be either 'routes', 'trucks', 'packages', or 'all'.")
