import unittest
from unittest.mock import MagicMock
from commands.view_trucks import ViewTrucksCommand
from core.application_data import ApplicationData
from models.truck_parking import TruckParking


class TestViewTrucksCommand(unittest.TestCase):

    def setUp(self):
        self.app_data = ApplicationData(TruckParking())

    def test_execute(self):
        command = ViewTrucksCommand([], self.app_data)

        # Test with empty parking
        result = command.execute()
        expected = "Truck parking is empty."
        self.assertEqual(result, expected)

        # Test with one truck in parking
        self.app_data.truck_parking.create_truck(MagicMock())
        result = command.execute()
        expected = "Truck parking:\nTruck ID 1: Type: None, Capacity: None, Max Range: None"
        self.assertEqual(result, expected)

        # Test with multiple trucks in parking
        self.app_data.truck_parking.create_truck(MagicMock())
        self.app_data.truck_parking.create_truck(MagicMock())
        result = command.execute()
        expected = "Truck parking:\nTruck ID 1: Type: None, Capacity: None, Max Range: None\nTruck ID 2: Type: None, Capacity: None, Max Range: None\nTruck ID 3: Type: None, Capacity: None, Max Range: None"
        self.assertEqual(result, expected)

if __name__ == '__main__':
    unittest.main()
