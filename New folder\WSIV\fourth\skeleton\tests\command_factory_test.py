import unittest
from commands.add_test import AddTestCommand
from commands.add_test_group import AddTestGroupCommand
from commands.add_test_run import AddTestRunCommand
from commands.remove_group import RemoveGroupCommand
from commands.test_report import TestReportCommand
from commands.view_group import ViewGroupCommand
from commands.view_system import ViewSystemCommand
from core.application_data import ApplicationData
from core.models_factory import ModelsFactory
from errors.application_error import ApplicationError
from models.test_run import TestRun
from core.command_factory import CommandFactory

class CommandFactory_Should(unittest.TestCase):
    def test_addTestCommandIsCreated(self):
        appData = ApplicationData()
        c_factory = CommandFactory(appData)
        i = c_factory.create("addtest 1 Description")
        self.assertIsInstance(i,AddTestCommand)

    def test_addTestGroupCommandIsCreated(self):
        appData = ApplicationData()
        c_factory = CommandFactory(appData)
        i = c_factory.create("addtestgroup MyGroup ")
        self.assertIsInstance(i,AddTestGroupCommand)

    def test_removeGroupCommandIsCreated(self):
        appData = ApplicationData()
        c_factory = CommandFactory(appData)
        i = c_factory.create("removegroup 1")
        self.assertIsInstance(i,RemoveGroupCommand)
    
    def test_addtestRunCommandIsCreated(self):
        appData = ApplicationData()
        c_factory = CommandFactory(appData)
        i = c_factory.create("addtestrun 1 fail 120")
        self.assertIsInstance(i,AddTestRunCommand)
    
    def test_addtestRunCommandIsCreated(self):
        appData = ApplicationData()
        c_factory = CommandFactory(appData)
        i = c_factory.create("addtestrun 1")
        self.assertIsInstance(i,AddTestRunCommand)
    
    def test_testReportCommandIsCreated(self):
        appData = ApplicationData()
        c_factory = CommandFactory(appData)
        i = c_factory.create("testreport 1")
        self.assertIsInstance(i,TestReportCommand)

    def test_ViewGroupCommandIsCreated(self):
        appData = ApplicationData()
        c_factory = CommandFactory(appData)
        i = c_factory.create("viewgroup 1")
        self.assertIsInstance(i,ViewGroupCommand)
    
    def test_ViewGroupCommandIsCreated(self):
        appData = ApplicationData()
        c_factory = CommandFactory(appData)
        i = c_factory.create("viewsystem")
        self.assertIsInstance(i,ViewSystemCommand)
    
    def test_returnApplicaitonError_ifCommandDoesNotExists(self):
        appData = ApplicationData()
        c_factory = CommandFactory(appData)
        with self.assertRaises(ApplicationError):
            i = c_factory.create("UnknownCommand")
