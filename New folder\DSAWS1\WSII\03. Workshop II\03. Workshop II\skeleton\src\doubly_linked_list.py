from src.linked_list_node import LinkedListNode


class DoublyLinkedList:
    def __init__(self):
        self._head: LinkedListNode = None
        self._tail: LinkedListNode = None
        self._count = 0

    @property
    def count(self):
        return self._count

    @property
    def head(self):
        return self._head

    @property
    def tail(self):
        return self._tail

    def add_first(self, value):
        node = LinkedListNode(value)
        if self.count == 0:
            self._tail = node
            self._head = self._tail
        else:
            node.next = self._head
            self._head.prev = node
            self._head = node
        self._count += 1

    def add_last(self, value):
        node = LinkedListNode(value)
        if self.count == 0:
            self._tail = node
            self._head = self._tail
        else:
            node.prev = self._tail
            self._tail.next = node
            self._tail = node
        self._count += 1

    def insert_after(self, node, value):
        new_node = LinkedListNode(value)
        curr_el = self._head
        if node == None:
            raise ValueError
        while curr_el != node:
            curr_el = curr_el.next
            if curr_el == None:
                break
        new_node.next = curr_el.next
        curr_el.next = new_node
        new_node.prev = curr_el
        if curr_el == self._tail:
            self._tail = new_node
        self._count += 1

    def insert_before(self, node, value):
        new_node = LinkedListNode(value)
        curr_el = self._head
        if node == None:
            raise ValueError
        while curr_el != node:
            curr_el = curr_el.next
            if curr_el == None:
                break
        if curr_el.prev != None:
            pre = curr_el.prev
            pre.next = new_node
            new_node.prev = pre
            new_node.next = curr_el
        else:
            new_node.next = curr_el
            curr_el.prev = new_node
            self._head = new_node
        curr_el.prev = new_node
        if curr_el == self._head:
            self._head = new_node
        self._count += 1

    def remove_first(self):
        if self._count == 0:
            raise ValueError
        removed_node = self._head
        self._head = self._head.next
        if self._head:
            self._head.prev = None
        self._count -= 1
        return removed_node.value

    def remove_last(self):
        if self._count == 0:
            raise ValueError
        removed_node = self._tail
        self._tail = self._tail.prev
        if self._tail:
            self._tail.next = None
        self._count -= 1
        return removed_node.value

    def find(self, value):
        if self._count == 0:
            return None
        curr_el = self._head
        while curr_el.value != value:
            curr_el = curr_el.next
            if curr_el == None:
                return None
        return curr_el

    def values(self):
        elements = []
        curr_el = self._head
        while curr_el is not None:
            elements.append(curr_el.value)
            curr_el = curr_el.next
        return tuple(elements)

    def _insert_before_head(self, value):
        pass

    def _insert_after_tail(self, value):
        pass
