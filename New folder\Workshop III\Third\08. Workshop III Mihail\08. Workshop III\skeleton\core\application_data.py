from models.test_group import TestGroup
from models.test import Test
from models.test_run import TestRun


class ApplicationData:
    def __init__(self):
        self._test_groups: list[TestGroup] = []

    @property
    def groups(self):
        return tuple(self._test_groups)

    def add_test_group(self, name: str):
        test_group = TestGroup(name)
        self._test_groups.append(test_group)

    def find_test_group_by_id(self, id) -> TestGroup:
        for tg in self._test_groups:
            if tg.id == id:
                return tg
        raise ValueError(f'Test group #{id} does not exists!')

    def find_test_by_id(self, id):
        for tg in self._test_groups:
            for t in tg.tests:
                if t.id == id:
                    return t

        raise ValueError(f'Test #{id} does not exists!')

    def remove_test_group_by_id(self, test_group):
        self._test_groups.remove(test_group)
