import unittest
from models.test_group import TestGroup
from errors.application_error import ApplicationError
from models.test import Test
VALID_ID = 2
VALID_NAME = 'TestOne'
VALID_TEST_ONE = Test(1, 'Valid test description one')
VALID_TEST_TWO = Test(2, 'Valid test description two')
EXPECTED_STR_OUTPUT_ZERO_TESTS = f'#{VALID_ID}. {VALID_NAME} (0 tests)'
EXPECTED_STR_OUTPUT_TWO_TESTS = f'#{VALID_ID}. {VALID_NAME} (2 tests)'
EXPECTED_VIEW_OUTPUT_ZERO_TESTS = EXPECTED_STR_OUTPUT_ZERO_TESTS 
EXPECTED_VIEW_OUTPUT_TWO_TESTS =(
    f'{EXPECTED_STR_OUTPUT_TWO_TESTS}'
    f'\n  {VALID_TEST_ONE.__str__()}'
    f'\n  {VALID_TEST_TWO.__str__()}'
    )
                                 
class TestGroup_Should(unittest.TestCase):
    
    def test_set_attributes(self):
        test_group = TestGroup( VALID_ID , VALID_NAME)
        self.assertEqual(VALID_ID,test_group.id)
        self.assertEqual(VALID_NAME, test_group.name)
        self.assertEqual(test_group._tests, [])

    def test_fail_when_NoName(self):
        with self.assertRaises(ApplicationError):
            test_group = TestGroup(VALID_ID ,'')

    def test_fail_when_nameIsNone(self):
        with self.assertRaises(ApplicationError):
            test_group = TestGroup(VALID_ID , None)

    def test_returns_tupleOfTests(self):
        test_group = TestGroup(VALID_ID , VALID_NAME)
        self.assertEqual(test_group.tests, tuple(test_group._tests))
    
    def test_adds_newTestWhenNotInGroup(self):
        test_group = TestGroup(VALID_ID , VALID_NAME)
        test_group.add_test(VALID_TEST_ONE)
        self.assertEqual(1, len(test_group.tests))

    def test_doesNot_add_duplicateTest(self):
        test_group = TestGroup(VALID_ID , VALID_NAME)
        test_group.add_test(VALID_TEST_ONE)
        test_group.add_test(VALID_TEST_ONE)
        self.assertEqual(1, len(test_group.tests))

    def test_str_returns_correctString_withZeroTests(self):
        test_group = TestGroup(VALID_ID , VALID_NAME)
        actual = test_group.__str__()
        self.assertEqual(actual, EXPECTED_STR_OUTPUT_ZERO_TESTS)

    def test_str_returns_correctString_withTwoTests(self):
        test_group = TestGroup(VALID_ID , VALID_NAME)
        test_group.add_test(VALID_TEST_ONE)
        test_group.add_test(VALID_TEST_TWO)
        actual = test_group.__str__()
        self.assertEqual(actual, EXPECTED_STR_OUTPUT_TWO_TESTS)

    def test_view_returns_correctString_withZeroTests(self):
        test_group = TestGroup(VALID_ID, VALID_NAME)
        test_view = test_group.view()
        self.assertEqual(test_view, EXPECTED_VIEW_OUTPUT_ZERO_TESTS)
        
    def test_view_returns_correctString_withTwoTests(self):
        test_group = TestGroup(VALID_ID, VALID_NAME)
        test_group.add_test(VALID_TEST_ONE)
        test_group.add_test(VALID_TEST_TWO)
        test_view = test_group.view()
        self.assertEqual(test_view, EXPECTED_VIEW_OUTPUT_TWO_TESTS)