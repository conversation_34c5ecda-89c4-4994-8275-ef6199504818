class Truck:
    COUNTER_ID = 1001
    
    def __init__(self, name: str, capacity: float, max_range: float):
        self._id = Truck.COUNTER_ID
        Truck.COUNTER_ID += 1
        
        self._name = name
        self._capacity = capacity
        self._max_range = max_range
    
    @property
    def id(self) -> int:
        return self._id
    
    @property
    def name(self) -> str:
        return self._name
    
    @property
    def capacity(self) -> float:
        return self._capacity
    
    @property
    def max_range(self) -> float:
        return self._max_range

    def __str__(self) -> str:
        return f'Truck ID: {self.id} | Name: {self.name} | Capacity: {self.capacity} | Max Range: {self.max_range}'

'''
actros = Truck("Actros", 50000, 9000)
scania = Truck("Scania", 42000, 8000)
man = Truck("MAN", 55000, 10000)
'''