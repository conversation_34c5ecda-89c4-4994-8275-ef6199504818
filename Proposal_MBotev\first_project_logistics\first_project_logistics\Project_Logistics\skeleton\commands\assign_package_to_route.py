from core.application_data import ApplicationData
from commands.validation_helpers import validate_params_count,try_parse_int


class AssignPackageToRouteCommand:

    def __init__(self, params, app_data: ApplicationData):
        validate_params_count(params,2)
        self._params = params
        self._app_data = app_data

    def execute(self):
        route_id = try_parse_int(self._params[0])
        package_id = try_parse_int(self._params[1])
        self._app_data.assign_package(route_id,package_id)
        
        return f'Package with ID: {package_id}  was assigned to Route with ID: {route_id}!'
    
    
