from core.application_data import ApplicationData
#from commands.validation_helpers import validate_params_count, try_parse_float


class UpdateRouteCommand:

    def __init__(self, params, app_data: ApplicationData):
        # validate_params_count(params, 3)
        self._params = params
        self._app_data = app_data

    def execute(self):

        id=self._params[0]
        try:
            route_id = int(id)
        except ValueError:
            return f"Invalid route id: {route_id}. Route id must be an integer."

        route = self._app_data.find_route_by_id(route_id)
        if route is None:
            return f"Route with id {route_id} not found."
        
        info_type = self._params[1]
        if info_type.lower() == 'assignpackage':
            try:
                package_id = int(self._params[2])
            except: ValueError('Invalid package id. Package id must be an integer.')

            package = self._app_data.find_package_by_id(package_id)
            if package is None:
                return f'Package with ID: {package_id} not found.'

            if package.route is not None: # Does it work?
                return f'Package with ID: {package_id} is already assigned to Route with ID: {package.route.id}.'

            if not route.can_add_package(package):# Does it work?
                return f'Route with ID: {route_id} has no space for Package with ID: {package_id}.'

            route.add_package(package)
            package.route = route # Does it work?
            return f'Package with ID: {package_id} was assigned to Route with ID: {route_id}.'

            
        elif info_type == 'assigntruck':
            try:
                truck_id = int(self._params[2])
            except: ValueError('Invalid truck id. Truck id must be an integer.')

            truck = self._app_data.find_truck_by_id(truck_id)
            route.assign_truck(truck)

        return f'Truck with ID {truck_id} was assigned to Route with ID {route_id}!'
            
        # id, departure_time, locations = self._params

        # route.departure_time = departure_time
        # route.locations = locations
        # return f"Route with id {id} was updated!"
