import unittest
from errors.application_error import ApplicationError
from models.test import Test
from models.test_run import TestRun

class TestRun_Should(unittest.TestCase):
    def test_initiallizeSuccessfully_withCorrectData(self):
        t = Test(1, "Lorem ipsum dolor sit amet")

        self.assertEqual(1 , t.id)
        self.assertEqual('Lorem ipsum dolor sit amet' , t.description)
        self.assertEqual(0 , len(t.test_runs))
    
    def test_returnApplicaitonError_withInccorectDescription(self):
        with self.assertRaises(ApplicationError):
            t = Test(1, None)
        with self.assertRaises(ApplicationError):
            t = Test(1, "")

    def test_returnAttributeError_whenTryChangeId(self):
        t = Test(1, "Lorem ipsum dolor sit amet")
        with self.assertRaises(AttributeError):
            t.id = 10

    def test_returnAttributeError_whenTryChangeDescription(self):
        t = Test(1, "Lorem ipsum dolor sit amet")
        with self.assertRaises(AttributeError):
            t.description = "Changed description."
    
    def test_returnAttributeError_whenTryAppendNewItemInTestRun(self):
        t = Test(1, "Lorem ipsum dolor sit amet")
        t_run = TestRun("pass", 150)
        with self.assertRaises(AttributeError):
            t.test_runs.append(t_run)
        
    def test_returnTuple_TestRunProperty(self):
        t = Test(1, "Lorem ipsum dolor sit amet")
        
        self.assertTrue(type(t.test_runs) is tuple)
    
    def test_returnTuple_passingTestRunsProperty(self):
        t = Test(1, "Lorem ipsum dolor sit amet")
        
        self.assertTrue(type(t.passing_test_runs) is tuple)

    def test_returnTuple_failedTestRunsProperty(self):
        t = Test(1, "Lorem ipsum dolor sit amet")
        
        self.assertTrue(type(t.failed_test_runs) is tuple)

    def test_returnCorrectTotalRuntime(self):
        t = Test(1, "Lorem ipsum dolor sit amet")
        t_run_1 = TestRun("fail", 150)
        t_run_2 = TestRun("pass", 150)
        t._test_runs.append(t_run_1)
        t._test_runs.append(t_run_2)

        self.assertEqual(300, t.total_runtime)
    
    def test_returnCorrectAvgRuntime(self):
        t = Test(1, "Lorem ipsum dolor sit amet")
        t_run_1 = TestRun("fail", 150)
        t_run_2 = TestRun("pass", 150)
        t._test_runs.append(t_run_1)
        t._test_runs.append(t_run_2)

        self.assertEqual(150, t.avg_runtime)

    def test_successfullyAddTestRun(self):
        t = Test(1, "Lorem ipsum dolor sit amet")
        t_run_1 = TestRun("fail", 150)
        t.add_test_run(t_run_1)
        self.assertEqual(1,len(t.test_runs))

    def test_returnCorrectlyForrmatedString_withGenerateReportMethodWithOneTestRun(self):
        t = Test(1, "Lorem ipsum dolor sit amet")
        t_run_1 = TestRun("fail", 150)
        t.add_test_run(t_run_1)

        should_return = "\n".join([
            '#1. [Lorem ipsum dolor sit amet]: 1 runs',
            '- Passing: 0',
            '- Failing: 0',
            '- Total runtime: 150ms',
            '- Average runtime: 150.0ms'
        ])
        
        self.assertEqual(should_return, t.generate_report())
    
    def test_returnCorrectlyForrmatedString_withGenerateReportMethodWithoutTestRun(self):
        t = Test(1, "Lorem ipsum dolor sit amet")

        should_return = "\n".join([
            '#1. [Lorem ipsum dolor sit amet]: 0 runs'
        ])
        
        self.assertEqual(should_return, t.generate_report())

    def test_returnCorrectlyForrmatedString_withSelfStringMethid(self):
        t = Test(1, "Lorem ipsum dolor sit amet")

        should_return = "\n".join([
            '#1. [Lorem ipsum dolor sit amet]: 0 runs'
        ])
        
        self.assertEqual(should_return, str(t))