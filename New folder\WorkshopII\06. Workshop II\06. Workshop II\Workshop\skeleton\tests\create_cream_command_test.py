import unittest
from unittest.mock import Mock

from commands.create_cream import C<PERSON><PERSON>reamCommand


def _create_fake_params(
        *,
        name='<PERSON><PERSON><PERSON>',
        brand='TrashyOverpricedBrand',
        price='10.99',
        gender='Men',
        scent= 'rose'):
    return [name, brand, price, gender, scent]


def _create_mock(*, product_exists_return_value: bool):
    fake_data = Mock()

    def product_exists(name):
        return product_exists_return_value

    def create_cream(name, brand, price, gender, scent):
        product = Mock()
        product.name = name
        fake_data.products.append(product)

        return product

    fake_data.products = []
    fake_data.product_exists = product_exists
    fake_data.create_cream = create_cream

    return fake_data


class CreateCreamCommandTest_Should(unittest.TestCase):
    def test_initializer_raisesError_tooFewParamsCount(self):
        # Arrange, Act & Assert
        with self.assertRaises(ValueError):
            cmd = CreateCreamCommand(['a'] * 4, <PERSON><PERSON>())

    def test_initializer_raisesError_tooManyParamsCount(self):
        # Arrange, Act & Assert
        with self.assertRaises(ValueError):
            cmd = CreateCreamCommand(['a'] * 6, Mock())

    def test_initializer_passes_validParamsCount(self):
        # Arrange & Act
        CreateCreamCommand(['a'] * 5, Mock())

        # Assert
        # nothing to assert

    def test_initializer_createsCream_validParams(self):
        # Arrange
        fake_params = _create_fake_params()
        cmd = CreateCreamCommand(
            fake_params,
            _create_mock(product_exists_return_value=False))

        # Act
        output = cmd.execute()

        # Assert
        self.assertEqual(
            f'Cream with name {fake_params[0]} was created!', output)

    def test_initializer_raisesError_productExists(self):
        # Arrange
        cmd = CreateCreamCommand(
            _create_fake_params(),
            _create_mock(product_exists_return_value=True))

        # Act & Assert
        with self.assertRaises(ValueError):
            cmd.execute()

    def test_initializer_raisesError_invalidPrice(self):
        # Arrange
        cmd = CreateCreamCommand(
            _create_fake_params(price='not a float'),
            _create_mock(product_exists_return_value=False))

        # Act & Assert
        with self.assertRaises(ValueError):
            cmd.execute()

    def test_initializer_raisesError_invalidGender(self):
        # Arrange
        cmd = CreateCreamCommand(
            _create_fake_params(gender='not a gender'),
            _create_mock(product_exists_return_value=False))

        # Act & Assert
        with self.assertRaises(ValueError):
            cmd.execute()

    def test_initializer_raisesError_invalidScent(self):
        # Arrange
        cmd = CreateCreamCommand(
            _create_fake_params(scent='not a scent'),
            _create_mock(product_exists_return_value=False))

        # Act & Assert
        with self.assertRaises(ValueError):
            cmd.execute()
