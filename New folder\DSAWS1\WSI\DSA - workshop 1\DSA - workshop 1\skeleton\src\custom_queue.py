
from src.linked_list_node import LinkedListNode


class CustomQueue:
    def __init__(self):
        self.head = None
        self.tail = None
        self.count = 0
    
    @property
    def is_empty(self):
        return self.count == 0 or self.head is None
    
    def enqueue(self, value):
        node = LinkedListNode(value)
        if self.tail:
            self.tail.next = node
        else:
            self.head = node
        
        self.tail = node
        self.count += 1
    
    def dequeue(self):
        if not self.head:
            raise ValueError("Queue is empty")
        value = self.head.value
        self.head = self.head.next
        if not self.head:
            self.tail = None
        self.count -= 1
        return value

    def peek(self):
        if not self.head:
            raise ValueError("Queue is empty")
        return self.head.value
    
    def __len__(self):
        return self.count

