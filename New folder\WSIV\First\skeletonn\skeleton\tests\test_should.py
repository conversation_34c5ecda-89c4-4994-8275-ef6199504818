import unittest
import tests.test_data as td
from models.test import Test
from models.test_run import TestRun
from errors.params_error import InvalidParamsTestClass

class Test_Should(unittest.TestCase):
    def test_canInstantiate(self):
        test = Test(td.VALID_ID_TEST, td.VALID_DESCRIPTION)

    def test_initializer_setsValuesValid(self):
        test = Test(td.VALID_ID_TEST, td.VALID_DESCRIPTION) 
        self.assertEqual(test.id, td.VALID_ID_TEST)
        self.assertEqual(test.description, td.VALID_DESCRIPTION)

    def test_init_raisesErrorWhen_nameIsEmpty(self):
        with self.assertRaises(InvalidParamsTestClass):
            test = Test(td.VALID_ID, td.INVALID_EMPTY_NAME)
            self.assertEqual("", td.INVALID_EMPTY_NAME)

    def test_init_raisesErrorWhen_nameIsNone(self):
        with self.assertRaises(InvalidParamsTestClass):
            test = Test(td.VALID_ID, td.INVALID_NONE_NAME)
            self.assertEqual(None, td.INVALID_EMPTY_NAME)

    def test_init_createsEmptyList(self):
        test = Test(td.VALID_ID, td.VALID_DESCRIPTION)
        self.assertEqual([], test._test_runs)

    def test_constructor_initilizesCorrectTestRunsTypeWhen_testRunsAdded(self):
        test = Test(td.VALID_ID, td.VALID_DESCRIPTION)
        self.assertIsInstance(test.test_runs, tuple)

    def test_constructor_initilizesPassingTestRunsTypeWhen_testPassesAdded(self):
        test = Test(td.VALID_ID, td.VALID_DESCRIPTION)
        self.assertIsInstance(test.passing_test_runs, tuple)

    def test_constructor_initilizesFailingTestRunsTypeWhen_testFailedAdded(self):
        test = Test(td.VALID_ID, td.VALID_DESCRIPTION)
        self.assertIsInstance(test.failed_test_runs, tuple)

    def test_constructor_initializingAvgRuntime_testCorrectValue(self):
        test_run_one = TestRun(td.TEST_RESULT, td.TEST_RESULT_ONE)
        test_run_two = TestRun(td.TEST_RESULT, td.TEST_RESULT_TWO)
        test_run_three = TestRun(td.TEST_RESULT, td.TEST_RESULT_THREE)
        test = Test(td.VALID_ID, td.VALID_DESCRIPTION)
        
        test.add_test_run(test_run_one)
        test.add_test_run(test_run_two)
        test.add_test_run(test_run_three)

        expected_avg_runtime = (1 + 2 + 3) / 3

        self.assertEqual(test.avg_runtime, expected_avg_runtime)

    def test_addTestRun_addsTestRunToList(self):
        test = Test(td.VALID_ID, td.VALID_DESCRIPTION)
        test_run = TestRun(td.TEST_RESULT, td.TEST_RESULT_ONE)
        test.add_test_run(test_run)
        self.assertEqual(1, len(test.test_runs))

    def test_generateReportMethod_returnsCorrectWhenEmpty(self):
        test = Test(td.VALID_ID, td.VALID_DESCRIPTION)
        self.assertEqual(td.EXPECTED_OUTPUT_EMPTY, test.generate_report())

    def test_generateReportMethod_returnsCorrectWhenNotEmpty(self):
        test = Test(td.VALID_ID, td.VALID_DESCRIPTION)
        test_run = TestRun(td.TEST_RESULT, td.TEST_RESULT_ONE)
        test.add_test_run(test_run)
        self.assertEqual(td.EXPECTED_OUTPUT_NOTEMPTY, test.generate_report())

    def test_magicStrMethod_returnsCorrectString(self):
        test = Test(td.VALID_ID, td.VALID_DESCRIPTION)
        test_run = TestRun(td.TEST_RESULT, td.TEST_RESULT_ONE)
        test.add_test_run(test_run)
        self.assertEqual(td.EXPECTED_MAGIC_STR, test.__str__())