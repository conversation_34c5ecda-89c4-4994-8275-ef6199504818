from core.application_data import ApplicationData

# from commands.validation_helpers import validate_params_count, try_parse_float


class SearchRouteCommand:

    def __init__(self, params, app_data: ApplicationData):
        # validate_params_count(params, 5)
        self._params = params
        self._app_data = app_data

    def execute(self):
        start, end, = self._params
        found_routes=self._app_data.search_route(start,end)
        return f'The following was found:\n{found_routes}'
    

