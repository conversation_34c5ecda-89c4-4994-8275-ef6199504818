
class Truck:
    def __init__(self, id, name, capacity, max_range):
        self._id = id
        self._name = name
        self._capacity = capacity
        self._max_range = max_range
        
    @property
    def id(self):
        return self._id
    
    @property
    def name(self):
        return self._name

    @property
    def capacity(self):
        return self._capacity

    @property
    def max_range(self):
        return self._max_range

    def __str__(self) -> str:
        return '\n'.join([
                f'Truck ID: {self.id}',
                f'Name: {self._name}',
                f'Capacity: {self._capacity}',
                f'Max range: {self._max_range}',
        ])
