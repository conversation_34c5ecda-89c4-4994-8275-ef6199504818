from commands.base.base_command import BaseCommand

class TestReport(BaseCommand):
    def execute(self):
        id = int(self._params[0])
        test = self.app_data.find_test(id)
        
        test_runs_count = len(test.test_runs)
        passing_runs_count = 0
        failing_runs_count = 0
        total_runtime = 0
        for test_run in test.test_runs:
            if test_run.test_result == 'pass':
                passing_runs_count += 1
            else:
                failing_runs_count += 1
            total_runtime += test_run.runtime_ms
        avg_runtime = total_runtime / test_runs_count
        
        return (
        f'#{test.id}. [{test.description}]: {test_runs_count} runs'
        + f'\n- Passing: {passing_runs_count}'
        + f'\n- Failing: {failing_runs_count}'
        + f'\n- Total runtime: {total_runtime}ms'
        + f'\n- Average runtime: {avg_runtime:.1f}ms'
        )