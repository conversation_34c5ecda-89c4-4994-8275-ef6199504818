from core.application_data import ApplicationData
from commands.validation_helpers import validate_params_count


class AssignTruckToRouteCommand:

    def __init__(self, params, app_data: ApplicationData):
        validate_params_count(params, 2)
        self._params = params
        self._app_data = app_data

    def execute(self):
        
        
        route_id = int(self._params[0])
        truck_name = self._params[1]
        
        
        # truck = self._app_data.trucks.get_truck(truck_id)
        # if not truck:
        #     return f"Truck with ID: {truck_id} not found!"

        # route = self._app_data.routes.get_route(route_id)
        # if not route:
        #     return f"Route with ID: {route_id} not found!"
        self._app_data.assign_truck(route_id,truck_name)
        # truck=self._app_data.truck_parking.find_truck(truck_name)
        
        # self._app_data.truck_parking.remove_truck(truck)
        # try:
        #     self._app_data.assign_truck(route_id,truck_id)
        # except ValueError as e:
        #     return str(e)
        
        return f"Truck: {truck_name} assigned to Route with ID: {route_id}!"
