from commands.create_package import Create<PERSON>ackageCommand
from commands.create_route import <PERSON><PERSON><PERSON>out<PERSON><PERSON>ommand
from commands.search_route import Search<PERSON>oute<PERSON>ommand
from commands.assign_truck_to_route import AssignTruckToRouteCommand
from commands.assign_package_to_route import AssignPackageToRouteCommand
from commands.view_routes import View<PERSON>out<PERSON><PERSON>ommand
from commands.view_packages import View<PERSON>ackagesCommand
from commands.view_trucks import ViewTrucksCommand

class CommandFactory:
    def __init__(self, data):
        self._app_data = data

    def create(self, input_line):
        cmd, *params = input_line.split()

        if cmd.lower() == "createpackage":
            return CreatePackageCommand(params, self._app_data)
        if cmd.lower() == "createroute":
            return CreateRouteCommand(params, self._app_data)
        if cmd.lower() == "searchroute":
            return SearchRouteCommand(params, self._app_data)
        if cmd.lower() == "assigntrucktoroute":
            return AssignTruckToRouteCommand(params, self._app_data)
        if cmd.lower() == "assignpackagetoroute":
            return AssignPackageToRouteCommand(params, self._app_data)
        if cmd.lower() == "viewroutes":
            return ViewRoutesCommand(params, self._app_data)
        if cmd.lower() == "viewpackages":
            return ViewPackagesCommand(params, self._app_data)
        if cmd.lower() == "viewtrucks":
            return ViewTrucksCommand(params, self._app_data)
        

        raise ValueError(f'Invalid command name: {cmd}!')
