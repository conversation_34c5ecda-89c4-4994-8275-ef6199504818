import unittest
from models.scania import Scania

VALID_NAME='Scania'
VALID_CAPACITY=23000
VALID_MAX_RANGE=10000

class ScaniaShould(unittest.TestCase):

    def test_initializer_RaisesErrorWhenIdIsLower(self):
        with self.assertRaises(ValueError):
            scania=Scania(1000,VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)

    def test_initializer_RaisesErrorWhenIdIsHigher(self):
        with self.assertRaises(ValueError):
            scania=Scania(1011,VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)