from core.application_data import ApplicationData
from models.route import Route
# from commands.validation_helpers import validate_params_count


class CreateRouteCommand:

    def __init__(self, params, app_data: ApplicationData):
        # validate_params_count(params, 3)
        self._params = params
        self._app_data = app_data

    def execute(self):
        departure_time = str(' '.join(self._params[:3]))
        locations=self._params[3]
        route=Route(departure_time,locations)
        self._app_data.add_route(route)
        return f'{route} was created!'
        
    
