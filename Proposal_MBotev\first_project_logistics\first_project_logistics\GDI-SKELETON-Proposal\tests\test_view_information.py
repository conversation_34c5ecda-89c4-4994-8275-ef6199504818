import unittest
from core.application_data import ApplicationData
from models.package import Package
from models.route import Route
from models.truck import Truck
from commands.create_package import CreatePackageCommand
from commands.create_route import CreateRouteCommand
from commands.assign_truck_to_route import AssignTruckToRouteCommand
from commands.assign_package_to_route import AssignPackageToRouteCommand
from commands.view_information import ViewInformationCommand

class TestViewInformation(unittest.TestCase):
    def setUp(self):
        self.app_data = ApplicationData()
        self.create_package_command = CreatePackageCommand(["SYD", "MEL", "45", "John", "Doe", "0123456789"], self.app_data)
        self.create_route_command = CreateRouteCommand(["06:00", "10-10-2022", "SYD", "MEL", "ADL"], self.app_data)
        self.assign_truck_command = AssignTruckToRouteCommand(["1001", "1"], self.app_data)
        self.assign_package_command = AssignPackageToRouteCommand(["1", "1"], self.app_data)
    def test_view_routes(self):
        self.create_route_command.execute()
        self.assign_truck_command.execute()
        view_information_command = ViewInformationCommand(["routes"], self.app_data)
        result = view_information_command.execute()
        self.assertIn("Route 1", result)
        self.assertIn("Departure Time: 06:00 10-10-2022", result)
        self.assertIn("Locations: ['SYD', 'MEL', 'ADL']", result)
        self.assertIn("Assigned Truck: 1001", result)

    def test_view_pending_packages(self):
        self.create_package_command.execute()
        view_information_command = ViewInformationCommand(["pending_packages"], self.app_data)
        result = view_information_command.execute()
        self.assertIn("Package 1", result)
        self.assertIn("Start Location: SYD", result)
        self.assertIn("End Location: MEL", result)
        self.assertIn("Weight: 45", result)
        self.assertIn("Contact Info: ['John', 'Doe', '0123456789']", result)

    def test_view_trucks(self):
        view_information_command = ViewInformationCommand(["trucks"], self.app_data)
        result = view_information_command.execute()
        self.assertIn("The following trucks are available:", result)
        self.assertIn("Truck 1001", result)
        self.assertIn("Name: Scania", result)
        self.assertIn("Capacity: 42000", result)
