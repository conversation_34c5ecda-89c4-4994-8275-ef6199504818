class Gender:
    MEN = 'Men'
    WOMEN = 'Women'
    UNISEX = 'Unisex'
    EVERY_DAY='Every_Day'
    MEDICAL='Medical'

    @classmethod
    def from_string(cls, gender_string):
        if gender_string not in [cls.MEN, cls.WOMEN, cls.UNISEX]:
            raise ValueError(
                f'None of the possible Gender values matches the value {gender_string}.')

        return gender_string
    
    @classmethod
    def shampoo_usage(cls,usage):
        if usage not in [cls.EVERY_DAY,cls.MEDICAL]:
            raise ValueError('None of the possible usages matches the value !')
        return usage


