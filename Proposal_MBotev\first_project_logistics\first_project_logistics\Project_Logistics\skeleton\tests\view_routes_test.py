import unittest
from unittest.mock import MagicMock
from commands.view_routes import ViewRoutesCommand
from core.application_data import ApplicationData
from models.route import Route


class TestViewRoutesCommand(unittest.TestCase):

    def test_execute(self):
        app_data = ApplicationData()
        cmd = ViewRoutesCommand(['Oct 10th 06:00h', 'BRI->SYD->MEL'], app_data)

        # No routes created yet
        self.assertEqual(cmd.execute(), 'No routes available!')

        # Add a route
        # app_data.add_route(Route('Oct 10th 06:00h', 'BRI->SYD->MEL'))

        # Route available on given date/time
        # self.assertEqual(cmd.execute(), "At that moment, the following route(s) is/are in progress:\nRoute ID 1: BRI -> SYD -> MEL\n Delivery weight: 0kg. Next stop: SYD (ETA: Oct 10th 20:00h)")

        # Route not available on given date/time
        # self.assertEqual(cmd.execute('Oct 9th 06:00h', 'BRI->SYD->MEL'), 'No routes available!')




















# import unittest
# from unittest.mock import MagicMock
# from commands.view_routes import ViewRoutesCommand
# from core.application_data import ApplicationData
# from models.route import Route


# class TestViewRoutesCommand(unittest.TestCase):

#     def test_execute(self):
#         app_data = ApplicationData()
#         cmd = ViewRoutesCommand(['2022-12-31 12:00'], app_data)

#         # No routes created yet
#         self.assertEqual(cmd.execute(), 'No routes available!')

#         # Add a route
#         app_data.add_route(Route('2022-12-31 09:00', 'MEL->SYD->BRI'))

#         # Route available on given date/time
#         self.assertEqual(cmd.execute(), 'At that moment, the following route(s) is/are in progress:\nRoute ID 1: MEL -> SYD -> BRI\n Delivery weight: 0kg. Next stop: SYD (ETA: 2022-12-31 12:30)')

#         # Route not available on given date/time
#         self.assertEqual(cmd.execute('2022-12-30 12:00'), 'No routes available!')
