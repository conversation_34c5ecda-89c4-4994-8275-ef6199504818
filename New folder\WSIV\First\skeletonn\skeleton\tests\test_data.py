from models.constants.test_result import TestResult

# TestRun
VALID_TEST_RESULT = TestResult.PASS
VALID_RUNTIME = 77
ZERO_RUNTIME = 0
NEGATIVE_RUNTIME = -77

# TestGroup
VALID_ID = 1
VALID_NAME = "SomeName"
VALID_TESTS_COUNT = 0
INVALID_NONE_NAME = None
INVALID_EMPTY_NAME = ''
TEST_DESCRIPTION = "SomeTest"
EXPECTED_OUTPUT = f'#{VALID_ID}. {VALID_NAME} ({VALID_TESTS_COUNT} tests)'

# Test
VALID_ID_TEST = 1
VALID_DESCRIPTION = "SomeTest"
TEST_RESULT = TestResult.PASS
TEST_RESULT_ONE = 1
TEST_RESULT_TWO = 2
TEST_RESULT_THREE = 3
TEST_RUNS = 0
TEST_RUNS_FOR_LIST_TEST = 1
FAILED_TESTS = 0
TEST_RESULT_PASSING = 1
EXPECTED_OUTPUT_EMPTY = f'#{VALID_ID_TEST}. [{VALID_DESCRIPTION}]: {TEST_RUNS} runs'
EXPECTED_OUTPUT_NOTEMPTY = f'''#{VALID_ID_TEST}. [{VALID_DESCRIPTION}]: {TEST_RUNS_FOR_LIST_TEST} runs
- Passing: {TEST_RESULT_PASSING}
- Failing: {FAILED_TESTS}
- Total runtime: {TEST_RESULT_ONE}ms
- Average runtime: {TEST_RESULT_ONE:.1f}ms'''
EXPECTED_MAGIC_STR = f'#{VALID_ID_TEST}. [{VALID_DESCRIPTION}]: {1} runs'

# Command Factory
ADDTEST_INPUT_LINE = "addTest testParams testGroupParams"
ADDTESTGROUP_INPUT_LINE = "addTestGroup 1 BigGroup"
REMOVEGROUP_INPUT_LINE = "removeGroup 1"
ADDTESTRUN_INPUT_LINE = "addTestRun testName 1 pass"
TESTREPORT_INPUT_LINE = "testReport 1"
VIEWGROUP_INPUT_LINE = "viewGroup 1"
VIEWSYSTEM_INPUT_LINE = "viewSystem 1"
INVALID_COMMAND = "invalidCommand someTest"

# ModelFactory
VALID_GROUP_NAME = "someGroup"
VALID_GROUP_NAME_TWO = "someGroupTwo"
VALID_TEST_DESCRIPTION = "someTest"
VALID_TEST_DESCRIPTION_TWO = "someTestTwo"
INVALID_VALUE = "INVALID"

# Application Data
VALID_NAME_TWO = "SomeNameTwo"
VALID_ID_TWO = 2
INVALID_ID = 5
VALID_ID_TEST = 2
VALID_DESCRIPTION_TEST = "SomeNameTwo"