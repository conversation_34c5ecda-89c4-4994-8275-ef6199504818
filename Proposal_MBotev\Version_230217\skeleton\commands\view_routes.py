from core.application_data import ApplicationData
from commands.validation_helpers import validate_params_count

class ViewRoutesCommand:

    def __init__(self, params, app_data: ApplicationData):
        validate_params_count(params, 3)
        self._params = params
        self._app_data = app_data

    def execute(self):
        request_time = str(' '.join(self._params[:]))
        info_route_on_date=self._app_data.available_routes_on_date(request_time)
        return f'At that moment, the following route(s) is/are in progress:\n{info_route_on_date}'
