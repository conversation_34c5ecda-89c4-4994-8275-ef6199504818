import unittest
from unittest.mock import Mock

from commands.create_package import CreatePackageCommand
from core.application_data import ApplicationData
from models.package import Package


class TestCreatePackageCommand(unittest.TestCase):

    def test_create_package_success(self):
        # Arrange
        app_data = ApplicationData()
        params = ["New York", "Los Angeles", "100.0", "John Doe", "<EMAIL>"]
        expected_output = "Package with ID: 1 to End location:Los Angeles was created!"
        command = CreatePackageCommand(params, app_data)

        # Act
        result = command.execute()

        # Assert
        self.assertEqual(result, expected_output)
        self.assertEqual(len(app_data.pending_packages.packages), 1)

        package = app_data.pending_packages.packages[0]
        self.assertIsInstance(package, Package)
        self.assertEqual(package.start_location, "New York")
        self.assertEqual(package.end_location, "Los Angeles")
        self.assertEqual(package.weight, 100.0)
        self.assertEqual(package.contact_info, ("<PERSON> Do<PERSON>", "<EMAIL>"))

    def test_create_package_missing_params(self):
        # Arrange
        app_data = ApplicationData()
        params = ["New York", "Los Angeles", "John Doe", "<EMAIL>"]
        command = CreatePackageCommand(params, app_data)

        # Act
        with self.assertRaises(ValueError):
            command.execute()
            self.assertEqual(len(app_data.pending_packages.packages), 0)
