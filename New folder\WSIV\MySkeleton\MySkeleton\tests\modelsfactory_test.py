import unittest
from models.test import Test
from models.test_group import TestGroup
from core.models_factory import ModelsFactory

class ModelsFactory_Should(unittest.TestCase):
    def setUp(self):
        self.factory = ModelsFactory()

    def test_create_group(self):
        group = self.factory.create_group("Test Group")
        self.assertIsInstance(group, TestGroup)
        self.assertEqual(group.name, "Test Group")
        self.assertEqual(group.id, 1)

    def test_create_group_id_incrementation(self):
        group1 = self.factory.create_group("Test Group 1")
        group2 = self.factory.create_group("Test Group 2")
        self.assertEqual(group1.id, 1)
        self.assertEqual(group2.id, 2)

    def test_create_test(self):
        test = self.factory.create_test("Test Description")
        self.assertIsInstance(test, Test)
        self.assertEqual(test.description, "Test Description")
        self.assertEqual(test.id, 1)

    def test_create_test_id_incrementation(self):
        test1 = self.factory.create_test("Test Description 1")
        test2 = self.factory.create_test("Test Description 2")
        self.assertEqual(test1.id, 1)
        self.assertEqual(test2.id, 2)