
from core.application_data import ApplicationData
from core.command_factory import CommandFactory
from core.engine import Engine
# from export_data import export_routes_to_csv, export_packages_to_csv  #GDI Export function 

app_data = ApplicationData()

# Add routes and packages to app_data
# export_routes_to_csv('routes.csv', app_data.routes)     #GDI Export function 
# export_packages_to_csv('packages.csv', app_data.pending_packages.packages)      #GDI Export function 

cmd_factory = CommandFactory(app_data)
engine = Engine(cmd_factory)

engine.start()






"""
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
#AssignTruck Actros 1
CreateRoute Oct 12th 06:00h SYD->MEL->ADL
#AssignTruck Actros 2
CreatePackage SYD MEL 45.0 <PERSON>, 1,Kings road, Melborne
SearchRoute SYD MEL
AssignPackageToRoute 1 1
end
CreatePackage ASP BRI 23000.0 Jeremy Inc., 23,Salt lake road, Brisbane
CreateRoute Sep 12th 06:00h ASP->ADL->MEL->SYD->BRI
#AssignTruck Actros 3
SearchRoute ASP BRI
AssignPackageToRoute 3 2
end
ViewPendingPackages
end
#ViewRoutes Oct 11th 8:00h
end

Assigntrucktoroute 1 1001
Viewtrucks
end

AddTruckToParking 1026 Actros
AddTruckToParking 1011 Man
AddTruckToParking 1001 Scania
Viewtrucks
Assigntrucktoroute 1 Actros
Viewtrucks
end

AddTruckToParking 1026 Actros
Viewtrucks
Assigntrucktoroute 1 Actros
Viewtrucks
end

CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
ViewPendingPackages
ViewRoutes
AssignPackageToRoute 1 1
ViewRoutes
ViewPendingPackages
end



-------------------------------------------------------------------------------
Use case #1
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreateRoute Oct 12th 06:00h SYD->MEL->ADL
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
SearchRoute SYD MEL
AssignPackageToRoute 1 1
ViewPackageInRoute 1 Oct 8th 10:38h
end

Use case #2
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreateRoute Oct 12th 06:00h SYD->MEL->ADL
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
AssignPackageToRoute 1 1
CreatePackage ASP BRI 23000.0 Jeremy Inc., 23,Salt lake road, Brisbane
CreateRoute Oct 12th 06:00h ASP->ADL->MEL->SYD->BRI
AssignPackageToRoute 3 2
ViewPackageInRoute 2 Oct 11th 10:38h
end

Use case #3
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreateRoute Oct 12th 06:00h SYD->MEL->ADL
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
AssignPackageToRoute 1 1
CreatePackage ASP BRI 23000.0 Jeremy Inc., 23,Salt lake road, Brisbane
CreateRoute Sep 12th 06:00h ASP->ADL->MEL->SYD->BRI
AssignPackageToRoute 3 2
ViewRoutes Sep 13th 06:38h
end

Use case #4-A
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
CreatePackage ASP BRI 23000.0 Jeremy Inc., 23,Salt lake road, Brisbane
ViewPendingPackages
end

Use case #4-B
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
CreatePackage ASP BRI 23000.0 Jeremy Inc., 23,Salt lake road, Brisbane
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreateRoute Oct 12th 06:00h SYD->MEL->ADL
CreateRoute Sep 12th 06:00h ASP->ADL->MEL->SYD->BRI
AssignPackageToRoute 1 1
AssignPackageToRoute 3 2
ViewPendingPackages
end

Use case #5
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreatePackage SYD MEL 45.0 John Smith, 1,Kings road, Melborne
AssignPackageToRoute 1 1
ViewPackageInRoute 1 Oct 11th 09:38h
end

Use Case #6
Viewtrucks
end

Use Case #7

AddTruckToParking 1026 Actros
AddTruckToParking 1011 Man
AddTruckToParking 1001 Scania
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
Viewtrucks
Assigntrucktoroute 1 Actros
Viewtrucks
end
"""
