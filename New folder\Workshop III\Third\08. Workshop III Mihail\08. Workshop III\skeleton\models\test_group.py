from models.test import Test


class TestGroup:
    test_group_id = 0

    @classmethod
    def next_group_id(cls):
        cls.test_group_id += 1
        return cls.test_group_id

    def __init__(self, name: str):
        self._id = TestGroup.next_group_id()
        self._name = name
        self._tests: list[Test] = []

    @property
    def id(self):
        return self._id

    @property
    def name(self):
        return self._name

    @property
    def tests(self):
        return tuple(self._tests)

    def add_test(self, test: Test):
        self._tests.append(test)

    def view_group(self):
        new_line = '\n'
        group_str = f'#{self.id}. {self.name} ({len(self._tests)} tests)\n' \
                     f'{new_line.join([t.test_to_string() for t in self._tests])}'
        return group_str

    def test_group_to_str(self):
        return f'  #{self.id}. {self.name} ({len(self._tests)} tests)'
