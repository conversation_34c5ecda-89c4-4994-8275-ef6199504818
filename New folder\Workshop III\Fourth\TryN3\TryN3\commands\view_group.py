from commands.base_command import BaseCommand
from core.application_data import ApplicationData


class ViewGroup(BaseCommand):
    def __init__(self, params: list[str], app_data: ApplicationData):
        super().__init__(params, app_data)

    def execute(self):
        test_group_id = self._app_data.find_test_group_by_id(int(self._params[0]))
        
        for test_group in self._app_data._test_groups:
            if test_group == test_group_id:
                # view_group = test_group

        
                output = f'#{test_group._id}. {test_group.name} ({len(test_group._tests)} tests)'
        
                for test in test_group_id._tests:
                    if test._id > 0:
                        output += f'\n  #{test._id}. [{test._description}]: {len(test._test_runs)} runs'
                    elif test._id == 0:
                        output += f'  #{test._id}. [{test._description}]: {len(test._test_runs)} runs'
        return output



'''ViewGroup (params: test_group_id) - returns formatted information about
a group with the given test_group_id. Formatting:

#{group_id}. {group_name} ({tests_count} tests)
  #{test_id}. [{test_description}]: {test_runs_count} runs
  #{test_id}. [{test_description}]: {test_runs_count} runs
'''