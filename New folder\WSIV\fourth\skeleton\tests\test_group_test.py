import unittest
from errors.application_error import ApplicationError
from models.test_group import TestGroup
from models.test import Test

class TestGroup_Should(unittest.TestCase):
    def test_initiallizeSuccessfully_withCorrectData(self):
        t = TestGroup(1,"Group Name")

        self.assertEqual(1, t.id)
        self.assertEqual("Group Name", t.name)
        self.assertEqual(0, len(t.tests))
   
    def test_raiseApplicationError_withIncorrectName(self):
        with self.assertRaises(ApplicationError):
            t = TestGroup(1,None)
        with self.assertRaises(ApplicationError):
            t = TestGroup(1,"")
    
    def test_returnTuple_when_testsPropertyIsCalled(self):
        t = TestGroup(1,"Group Name")

        self.assertTrue(type(t.tests) is tuple)
    
    def test_returnAttributeError_whenTryChangeId(self):
        t = TestGroup(1,"Group Name")
        with self.assertRaises(AttributeError):
            t.id = 11

    def test_returnAttributeError_whenTryChangeName(self):
        t = TestGroup(1,"Group Name")
        with self.assertRaises(AttributeError):
            t.name = "New Name"
    
    def test_AddTestSuccessfully_withCorrectData(self):
        t_group = TestGroup(1,"Group Name")
        t = Test(1, "Fake test description.")
        t_group.add_test(t)
        
        self.assertEqual(1 ,len(t_group.tests))
        self.assertEqual("Fake test description." ,t_group.tests[0].description)
    
    def test_notAbleToAddOneTestTwice(self):
        t_group = TestGroup(1,"Group Name")
        t = Test(1, "Fake test description.")
        t_group.add_test(t)
        t_group.add_test(t)
        
        self.assertEqual(1, len(t_group.tests))

    def test_returnCorrectFormattedString_withViewMethod(self):
        t_group = TestGroup(1,"Group Name")
        t = Test(1, "Fake test description.")
        t_group.add_test(t)
        must_return = "\n".join([
            '#1. Group Name (1 tests)',
            '  #1. [Fake test description.]: 0 runs'
        ])

        self.assertEqual(must_return, t_group.view())

    def test_returnCorrectFormattedString_withSelfStringMethod(self):
        t_group = TestGroup(1,"Group Name")
        t = Test(1, "Fake test description.")
        t_group.add_test(t)     
        must_return = '#1. Group Name (1 tests)'

        self.assertEqual(must_return, str(t_group))