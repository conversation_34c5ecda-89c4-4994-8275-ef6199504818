
from src.linked_list_node import LinkedListNode


class CustomStack:
    def __init__(self):
        self.top = None
        self._count = 0

    def push(self, value):
        new_node = LinkedListNode(value)
        new_node.next = self.top
        self.top = new_node
        self._count += 1

    def pop(self):
        if not self.top:
            raise ValueError("Stack is empty")
        value = self.top.value
        self.top = self.top.next
        self._count -= 1
        return value

    def peek(self):
        if not self.top:
            raise ValueError("Stack is empty")
        return self.top.value

    @property
    def count(self):
        return self._count

    @property
    def is_empty(self):
        return self._count == 0
