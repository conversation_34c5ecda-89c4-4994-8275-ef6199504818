import unittest
from datetime import datetime
from models.route import Route
from models.location import Location
from core.application_data import ApplicationData
from commands.search_route import SearchRouteCommand


class TestSearchRouteCommand(unittest.TestCase):

    def setUp(self):
        self.app_data = ApplicationData()
        self.locations = [Location('BRI'), Location('SYD'), Location('MEL'), Location('ADL'), Location('PER')]
        self.routes = [Route(datetime(2022, 1, 1, 6, 0), self.locations[:3]),  # route 1
                       Route(datetime(2022, 1, 1, 12, 0), self.locations[2:4]),  # route 2
                       Route(datetime(2022, 1, 2, 6, 0), self.locations[1:]),  # route 3
                       Route(datetime(2022, 1, 3, 6, 0), self.locations[:2] + self.locations[3:])]  # route 4
        for route in self.routes:
            self.app_data.add_route(route)

    def test_search_route_command(self):
        # Test search for existing route
        command = SearchRouteCommand(['SYD', 'MEL'], self.app_data)
        result = command.execute()
        self.assertEqual(result, "The following was found:\nJan 01 2022 06:00AM: BRI->SYD->MEL")

        # Test search for non-existing route
        command = SearchRouteCommand(['BRI', 'PER'], self.app_data)
        with self.assertRaises(ValueError):
            command.execute()

if __name__ == '__main__':
    unittest.main()
