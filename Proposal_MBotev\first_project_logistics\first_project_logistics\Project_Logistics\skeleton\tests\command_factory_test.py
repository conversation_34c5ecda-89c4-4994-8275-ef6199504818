import unittest
from core.command_factory import CommandFactory
from core.application_data import ApplicationData
from commands.create_package import Create<PERSON><PERSON>age<PERSON>ommand
from commands.create_route import CreateRouteCommand
from commands.search_route import SearchRouteCommand
from commands.assign_truck_to_route import AssignTruckToRouteCommand
from commands.assign_package_to_route import AssignPackageToRouteCommand
from commands.view_routes import View<PERSON>out<PERSON><PERSON>ommand
from commands.view_pending_packages import ViewPendingPackagesCommand
from commands.view_package_in_route import ViewPackageInRouteCommand
from commands.view_trucks import ViewTrucksCommand

class CommandFactory_Should(unittest.TestCase):
    def test_create_raiseErrorWhenCommandNotValid(self):
        app_data=ApplicationData()
        cf=CommandFactory(app_data)

        with self.assertRaises(ValueError):
            cf.create('addSomething 1 ShouldWork_WhenToldTo')

    def test_createpackage_CreatedPackagesWhenCommandCorrect(self):
        
        input_line='CreatePackage SYD MEL 45.0 <PERSON>, 1,<PERSON> road, Melborne'

        app_data=ApplicationData()
        cf=CommandFactory(app_data)
        command=cf.create(input_line)

        self.assertIsInstance(command,CreatePackageCommand,'Test failed')

    def test_createroute_CreatedRouteWhenCommandCorrect(self):
        
        input_line='CreateRoute Oct 10th 06:00h BRI->SYD->MEL'

        app_data=ApplicationData()
        cf=CommandFactory(app_data)
        command=cf.create(input_line)

        self.assertIsInstance(command,CreateRouteCommand,'Test failed')

    def test_searchroute_SearchRouteWhenCommandCorrect(self):
        
        input_line='SearchRoute SYD MEL'

        app_data=ApplicationData()
        cf=CommandFactory(app_data)
        command=cf.create(input_line)

        self.assertIsInstance(command,SearchRouteCommand,'Test failed')

    def test_assigntrucktoroute_AssignTruckToRouteWhenCommandCorrect(self):
        
        input_line='Assigntrucktoroute 1 Actros'

        app_data=ApplicationData()
        cf=CommandFactory(app_data)
        command=cf.create(input_line)

        self.assertIsInstance(command,AssignTruckToRouteCommand,'Test failed')

    def test_assignpackagetoroute_AssignPackageToRouteWhenCommandCorrect(self):
        
        input_line='AssignPackageToRoute 1 1'

        app_data=ApplicationData()
        cf=CommandFactory(app_data)
        command=cf.create(input_line)

        self.assertIsInstance(command,AssignPackageToRouteCommand,'Test failed')

    def test_viewroutes_ViewRoutesWhenCommandCorrect(self):
        
        input_line='ViewRoutes Sep 13th 06:38h'

        app_data=ApplicationData()
        cf=CommandFactory(app_data)
        command=cf.create(input_line)

        self.assertIsInstance(command,ViewRoutesCommand,'Test failed')

    def test_viewpendingpackages_ViewPandingPackagesWhenCommandCorrect(self):
        
        input_line='ViewPendingPackages'

        app_data=ApplicationData()
        cf=CommandFactory(app_data)
        command=cf.create(input_line)

        self.assertIsInstance(command,ViewPendingPackagesCommand,'Test failed')

    def test_viewpackagesinroute_ViewPackageInRouteWhenCommandCorrect(self):
        
        input_line='ViewPackageInRoute 1 Oct 11th 09:38h'

        app_data=ApplicationData()
        cf=CommandFactory(app_data)
        command=cf.create(input_line)

        self.assertIsInstance(command,ViewPackageInRouteCommand,'Test failed')

    def test_viewtrucks_ViewTrucksWhenCommandCorrect(self):
        
        input_line='Viewtrucks'

        app_data=ApplicationData()
        cf=CommandFactory(app_data)
        command=cf.create(input_line)

        self.assertIsInstance(command,ViewTrucksCommand,'Test failed')