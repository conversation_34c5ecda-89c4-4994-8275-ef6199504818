import unittest
from unittest.mock import MagicMock
from commands.view_routes import ViewRout<PERSON>Command
from core.application_data import ApplicationData


class TestViewRoutesCommand(unittest.TestCase):

    def test_execute(self):
        app_data = ApplicationData()
        cmd = ViewRoutesCommand(['2022-12-31 12:00'], app_data)

        # No routes created yet
        self.assertEqual(cmd.execute(), 'No routes available!')

        # Add a route
        app_data.add_route(Route('2022-12-31 09:00', 'MEL->SYD->BRI'))

        # Route available on given date/time
        self.assertEqual(cmd.execute(), 'At that moment, the following route(s) is/are in progress:\nRoute ID 1: MEL -> SYD -> BRI\n Delivery weight: 0kg. Next stop: SYD (ETA: 2022-12-31 12:30)')

        # Route not available on given date/time
        self.assertEqual(cmd.execute('2022-12-30 12:00'), 'No routes available!')
