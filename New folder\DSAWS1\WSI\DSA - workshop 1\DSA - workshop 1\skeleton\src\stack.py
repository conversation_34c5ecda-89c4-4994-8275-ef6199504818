
from src.linked_list_node import LinkedListNode


class CustomStack:
    def __init__(self):
        self.top = None
        self.count = 0

    def push(self, value):
        new_node = LinkedListNode(value)
        new_node.next = self.top
        self.top = new_node
        self.count += 1

    def pop(self):
        if self.is_empty:
            raise ValueError("Stack is empty")
        value = self.top.value
        self.top = self.top.next
        self.count -= 1
        return value

    def peek(self):
        if self.is_empty:
            raise ValueError("Stack is empty")
        return self.top.value

    @property
    def is_empty(self):
        return self.count == 0 or self.top is None

    def __len__(self):
        return self.count