from models.package import Package


class PendingPackages:
    """
    Contains all created packages that are still not assign to a route.
    """
    def __init__(self) -> None:
        self._pending_packages: list[Package]=[]

    @property
    def pending_packages(self):
        return tuple(self._pending_packages)
    
    def create_package(self,package: Package)->None:
        self._pending_packages.append(package)
    
    def find_package(self,package_id: int)->Package:
        founded=[package for package in self._pending_packages if package.id == package_id]
        if founded == []:
            raise ValueError('No Package found!')
        return founded[0]
    
    def remove_package(self,package: Package) -> None:
        self._pending_packages.remove(package)

    
    def __str__(self) -> str:
        if self._pending_packages == []:
            return f'/!\ No pending packages. Have a break!'
        packages = '\n*************\n'.join(f'#Package ID:{p.id} | Start location: {p.start_location} | End location: {p.end_location}' for p in self._pending_packages)
        return f'  Pending Packages Information:\n'+f'{packages}'
