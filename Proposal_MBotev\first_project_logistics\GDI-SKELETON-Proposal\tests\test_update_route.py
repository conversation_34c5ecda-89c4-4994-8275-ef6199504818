import unittest
from core.application_data import ApplicationData
from models.route import Route
from models.package import Package
from models.truck import Truck
from commands.create_route import CreateRouteCommand
from commands.create_package import CreatePackageCommand
from commands.assign_truck_to_route import AssignTruckToRouteCommand
from commands.assign_package_to_route import AssignPackageToRouteCommand

class TestUpdateRoute(unittest.TestCase):

    def setUp(self):
        self.app_data = ApplicationData()
        self.route = Route("Oct 12th 06:00h", ["Alice Springs", "Adelaide", "Melbourne", "Sydney", "Brisbane"])
        self.package = Package("Sydney", "Melbourne", 45, "John Doe", "<EMAIL>")
        self.truck = Truck(1001, "Scania", 42000, 8000)

    def test_assign_truck_to_route(self):
        # create the route
        create_route_cmd = CreateRouteCommand(["Oct 12th 06:00h", "Alice Springs", "Adelaide", "Melbourne", "Sydney", "Brisbane"], self.app_data)
        route = create_route_cmd.execute()
        # assign the truck to the route
        assign_truck_to_route_cmd = AssignTruckToRouteCommand([route.id, self.truck.id], self.app_data)
        assign_truck_to_route_cmd.execute()
        # check if the truck was assigned to the route
        self.assertEqual(self.app_data.routes[0].truck.id, self.truck.id)

    def test_assign_package_to_route(self):
        # create the route
        create_route_cmd = CreateRouteCommand(["Oct 12th 06:00h", "Alice Springs", "Adelaide", "Melbourne", "Sydney", "Brisbane"], self.app_data)
        route = create_route_cmd.execute()
        # create the package
        create_package_cmd = CreatePackageCommand(["Sydney", "Melbourne", 45, "John Doe", "<EMAIL>"], self.app_data)
        package = create_package_cmd.execute()
        # assign the package to the route
        assign_package_to_route_cmd = AssignPackageToRouteCommand([route.id, package.id], self.app_data)
        assign_package_to_route_cmd.execute()
        # check if the package was assigned to the route
        self.assertIn(package, self.app_data.routes[0].packages)

if __name__ == '__main__':
    unittest.main()