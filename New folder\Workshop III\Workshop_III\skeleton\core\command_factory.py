
from core.application_data import ApplicationData
from commands.base.add_test_group import AddTestGroup
from commands.base.add_test_run import AddTestRun
from commands.base.add_test import AddTest
from commands.base.remove_group import RemoveGroup
from commands.base.test_report import TestReport
from commands.base.view_group import ViewGroup
from commands.base.view_system import ViewSystem


class CommandFactory:
    def __init__(self, data: ApplicationData):
        self._app_data = data

    def create(self, command_line):
        command, *params = command_line.split()

        if command.lower() == 'addtestgroup':
            return AddTestGroup(params, self._app_data)
        if command.lower() == 'addtestrun':
            return AddTestRun(params, self._app_data)
        if command.lower() == 'addtest':
            return AddTest(params, self._app_data)
        if command.lower() == 'removegroup':
            return RemoveGroup(params, self._app_data)
        if command.lower() == 'testreport':
            return TestReport(params, self._app_data)
        if command.lower() == 'viewgroup':
            return ViewGroup(params, self._app_data)
        if command.lower() == 'viewsystem':
            return ViewSystem(params, self._app_data)
        