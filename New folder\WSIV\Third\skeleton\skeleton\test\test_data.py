VALID_NAME='TestName'
VALID_RESULT = 'TestResult'
VALID_RUNTIME = 'TestBRuntime'
VALID_ID = 'TestId'
VALID_DESCRIPTION = 'TestDescription'
VALID_PASSING='PassingTestRuns'
VALID_fAILING='FailingTestRuns'
VALID_TOTAL_RUNTIME='TotalRunTime'
VALID_AVG_RUNTIME='Average_Runtime'
VALID_TESTRUNS='TestRuns'
VALID_ADDTEST_COMMAND = 'addtest'
VALID_ADDTESTGROUP_COMMAND = 'addtestgroup'
VALID_REMOVEGROUP_COMMAND = 'removegroup'
VALID_ADDTESTRUN_COMMAND = 'addtestrun'
VALID_TESTREPORT_COMMAND = 'testreport'
VALID_VIEWGROUP_COMMAND = 'viewgroup'
VALID_VIEWSYSTEM_COMMAND = 'viewsystem'

EXPECTED_OUTPUT =(
    f'''{self}',
    - Passing: {len(VALID_PASSING)}',
    - Failing: {len(VALID_fAILING)}',
    - Total runtime: {VALID_TOTAL_RUNTIME}ms',
    - Average runtime: {VALID_AVG_RUNTIME}ms'
''')
