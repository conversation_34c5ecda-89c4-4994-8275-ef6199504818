
from src.linked_list_node import LinkedListNode


class CustomStack:
    def __init__(self):
        self._top: LinkedListNode = None
        self._size = 0

    def push(self, value):
        if self._top is None:
            self._top = LinkedListNode(value)
        else:
            new_node = LinkedListNode(value)
            new_node.next = self._top
            self._top = new_node
        self._size += 1

    def pop(self):
        if not self.is_empty:
            value = self._top.value
            self._top = self._top.next
            self._size -= 1
            return value
        raise ValueError('Stack is empty')

    def peek(self):
        if not self.is_empty:
            return self._top.value
        raise ValueError('Stack is empty')
        
    @property
    def count(self):
        return self._size

    @property
    def is_empty(self):
        return self._size == 0
