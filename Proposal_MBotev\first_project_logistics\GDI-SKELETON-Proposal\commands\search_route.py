from core.application_data import ApplicationData
#from commands.validation_helpers import validate_params_count, try_parse_float


class SearchRouteCommand:

    def __init__(self, params, app_data: ApplicationData):
    # validate_params_count(params, 5)
        self._params = params
        self._app_data = app_data

    def execute(self):
        start, end, = self._params[:2]
        found_routes = self._app_data.search_route(start, end)
        if not found_routes:
            return f"No routes found between {start} and {end}"
        results = '\n'.join([str(route) for route in found_routes])
        return f'The following routes were found between {start} and {end}:\n{results}'





#Mbotev
# from core.application_data import ApplicationData

# # from commands.validation_helpers import validate_params_count, try_parse_float


# class SearchRouteCommand:

#     def __init__(self, params, app_data: ApplicationData):
#         # validate_params_count(params, 5)
#         self._params = params
#         self._app_data = app_data

#     def execute(self):
#         start, end, = self._params
#         found_routes=self._app_data.search_route(start,end)
#         return f'The following was found:\n{found_routes}'
    

