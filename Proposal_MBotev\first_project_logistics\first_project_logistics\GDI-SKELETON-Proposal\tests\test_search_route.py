import unittest
from core.application_data import ApplicationData
from models.route import Route
from models.package import Package
from commands.create_route import Create<PERSON>oute<PERSON>ommand
from commands.create_package import CreatePackageCommand
from commands.search_route import SearchRouteCommand

class TestSearchRoute(unittest.TestCase):

    def setUp(self):
        self.app_data = ApplicationData()
        self.route_params = ['06:00', 'SYD', 'MEL', 'ADL']
        self.package_params = ['SYD', 'ADL', '45', '<PERSON>', '<EMAIL>', '0412345678']
        
    def test_search_route(self):
        # create a route
        route_command = CreateRouteCommand(self.route_params, self.app_data)
        route_command.execute()

        # create a package
        package_command = CreatePackageCommand(self.package_params, self.app_data)
        package_command.execute()

        # search for the route
        search_params = ['SYD', 'ADL']
        search_route_command = SearchRouteCommand(search_params, self.app_data)
        result = search_route_command.execute()
        
        # assert the result
        self.assertIn('Route', result)
        self.assertIn('Departure Time', result)
        self.assertIn('Locations', result)

if __name__ == '__main__':
    unittest.main()