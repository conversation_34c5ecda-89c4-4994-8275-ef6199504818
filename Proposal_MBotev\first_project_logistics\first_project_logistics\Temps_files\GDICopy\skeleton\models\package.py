from datetime import datetime

class Package:
    counter=1
    def __init__(self,start_location: str,end_location: str,weight: float,contact_info: str) -> None:
        self._id=Package.counter
        Package.counter+=1
        self._start_location=start_location
        self._end_location=end_location
        self._weight=weight
        self._contact_info=contact_info
        self._registration_time=datetime.now()

    @property
    def id(self):
        return self._id
    
    @property
    def registration_time(self):
        return self._registration_time
    
    @property
    def start_location(self):
        return self._start_location
    
    @property
    def end_location(self):
        return self._end_location
    
    @property
    def weight(self):
        return self._weight
    
    @property
    def contact_info(self):
        return self._contact_info
    
    def __str__(self) -> str:
        return f"Package id: {self.id}\nDate of registration: {self.registration_time}\n"
    
    
    
    
    


    # raise NotImplementedError()