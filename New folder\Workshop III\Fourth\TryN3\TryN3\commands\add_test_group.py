from commands.base_command import BaseCommand
from core.application_data import ApplicationData
from models.test_group import TestGroup

class AddTestGroup(BaseCommand):
    counter_id = 0
    def __init__(self, params: list[str], app_data: ApplicationData):
        AddTestGroup.counter_id += 1
        self._group_counter = AddTestGroup.counter_id
        super().__init__(params, app_data)

    def execute(self):
        name = self._params[0]
        new_test_group = TestGroup(self._group_counter, name)
        self._app_data._test_groups.append(new_test_group)
        return f'Group #{new_test_group.id} created'



#addtestgroup TransactionTests