import unittest
from core.models_factory import ModelsFactory
from errors.application_error import ApplicationError
from models.test_run import TestRun

class ModelsFactory_Should(unittest.TestCase):
    def test_createsGroupSuccessfully(self):
        m_factory = ModelsFactory()
        group = m_factory.create_group("Test Group")
        self.assertEqual("Test Group", group.name)
    
    def test_increaseGroupIdSuccessfully(self):
        m_factory = ModelsFactory()
        group = m_factory.create_group("Test Group")
        group_2 = m_factory.create_group("Second test group")

        self.assertEqual(2,group_2.id)

    def test_createsTestSuccessfully(self):
        m_factory = ModelsFactory()
        tst = m_factory.create_test("Test description.")

        self.assertEqual("Test description.", tst.description)

    def test_increaseGroupIdSuccessfully(self):
        m_factory = ModelsFactory()
        tst = m_factory.create_test("Test description.")
        tst_2 = m_factory.create_test("Test description - 2.")

        self.assertEqual(2,tst_2.id)

    def test_createsTestRunSuccessfully(self):
        m_factory = ModelsFactory()
        test_run = m_factory.create_test_run("fail", "110")

        self.assertIsInstance(test_run,TestRun)

    def test_raiseApplicaitonError_whenTestResultIsIncorrectData(self):
        m_factory = ModelsFactory()
        with self.assertRaises(ApplicationError):
            test_run = m_factory.create_test_run("FAIL", "110")

    def test_raiseApplicaitonError_whenTestResultIsIncorrectData(self):
        m_factory = ModelsFactory()
        with self.assertRaises(ApplicationError):
            test_run = m_factory.create_test_run("fail", "110s")
