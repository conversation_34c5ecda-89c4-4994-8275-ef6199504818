from core.application_data import ApplicationData
from models.gender import Gender
from commands.validation_helpers import validate_params_count, try_parse_float


class CreateToothpasteCommand:

    def __init__(self, params, app_data: ApplicationData):
        validate_params_count(params, 5)
        self._params = params
        self._app_data = app_data

    def execute(self):
        tooth_paste_name = self._params[0]
        if self._app_data.product_exists(tooth_paste_name):
            raise ValueError(f'Toothpaste with name {tooth_paste_name} already exists!')

        self._app_data.create_toothpaste(tooth_paste_name)

        return f'Toothpaste with name {tooth_paste_name} was created!'
