import unittest
from errors.application_error import ApplicationError
from models.test import Test
from models.constants.test_result import TestResult
VALID_ID = 2
VALID_NAME = 'TestOne'
EXPECTED_STR_OUTPUT_ZERO_RUNS = f'#{VALID_ID}. [{VALID_NAME}]: 0 runs'
EXPECTED_STR_OUTPUT_TWO_RUNS = f'#{VALID_ID}. [{VALID_NAME}]: 2 runs'
VALID_TEST_RESULT_PASS = 'pass'
VALID_TEST_RESULT_FAIL = 'fail'
                                 
class Test_Should(unittest.TestCase):
    
    def test_set_attributes(self):
        test = Test(VALID_ID, VALID_NAME)
        self.assertEqual(VALID_ID,test.id)
        self.assertEqual(VALID_NAME, test.description)
        self.assertEqual(test._test_runs, [])

    def test_fail_when_NoDescription(self):
        with self.assertRaises(ApplicationError):
            test_group = Test(VALID_ID ,'')

    def test_fail_when_DescriptionIsNone(self):
        with self.assertRaises(ApplicationError):
            test_group = Test(VALID_ID , None)

    def test_returns_tupleOfTestRuns(self):
        test = Test(VALID_ID , VALID_NAME)
        self.assertEqual(test.test_runs, tuple(test._test_runs))
    
    def test_returns_correctNumber_ofPassingRuns(self):
        test = Test(VALID_ID , VALID_NAME)
        test.add_test_run(VALID_TEST_RESULT_PASS)
        test.add_test_run(VALID_TEST_RESULT_FAIL)
        passing = test.passing_test_runs
        self.assertEqual(1, len(passing))

    def test_adds_newTestRun_withDifferentResults(self):
        test = Test(VALID_ID , VALID_NAME)
        test.add_test_run(VALID_TEST_RESULT_PASS)
        test.add_test_run(VALID_TEST_RESULT_FAIL)
        self.assertEqual(2, len(test.test_runs))

    def test_adds_newTestRun_withSameResults(self):
        test = Test(VALID_ID , VALID_NAME)
        test.add_test_run(VALID_TEST_RESULT_PASS)
        test.add_test_run(VALID_TEST_RESULT_PASS)
        self.assertEqual(2, len(test.test_runs))

    def test_returns_correctString_withZeroRuns(self):
        test = Test(VALID_ID , VALID_NAME)
        string = test.__str__()
        self.assertEqual(string, EXPECTED_STR_OUTPUT_ZERO_RUNS)

    def test_returns_correctString_withTwoRuns(self):
        test = Test(VALID_ID , VALID_NAME)
        test.add_test_run(VALID_TEST_RESULT_PASS)
        test.add_test_run(VALID_TEST_RESULT_FAIL)
        string = test.__str__()
        self.assertEqual(string, EXPECTED_STR_OUTPUT_TWO_RUNS)