import unittest
from errors.application_error import ApplicationError
from models.test_run import TestRun

class TestRun_Should(unittest.TestCase):
    def test_initiallizeSuccessfully_withCorrectData(self):
        t = TestRun('pass', 100)
        self.assertEqual('pass', t.test_result)
    
    def test_initiallizerReturnApplicationError_withIncorrect_Runtime(self):
        with self.assertRaises(ApplicationError):
            t = TestRun('pass', -100)
        with self.assertRaises(ApplicationError):
            t = TestRun('pass', 0)

    def test_initiallizerReturnApplicationError_withIncorrect_TestResult(self):
        with self.assertRaises(ApplicationError):
            t = TestRun('ppass', -100)
    
    def test_returnAttributeError_whenTryChangeTestResults(self):
        t = TestRun('pass', 100)
        
        with self.assertRaises(AttributeError):
            t.test_result = 'fail'

    def test_returnAttributeError_whenTryChangeRuntimeMS(self):
        t = TestRun('pass', 100)
        
        with self.assertRaises(AttributeError):
            t.runtime_ms = 150