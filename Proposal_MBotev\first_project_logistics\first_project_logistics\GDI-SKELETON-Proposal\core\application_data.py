from models.package import Package
from models.route import Route
from models.truck import Truck

class ApplicationData:
    def __init__(self):
        self._routes: list[Route] = []
        self._packages: list[Package] = []
        self._trucks: list[Truck] = []

    @property
    def routes(self):
        return tuple(self._routes)
    
    @property
    def packages(self):
        return tuple(self._packages)
    
    @property
    def trucks(self):
        return tuple(self._trucks)

    def add_route(self, route: Route) -> None:
        self._routes.append(route)

    def add_package(self, package: Package) -> None:
        self._packages.append(package)

    def add_truck(self, truck: Truck) -> None:
        self._trucks.append(truck)

    def search_route(self, start, end) -> list[Route]:
        founded_routes = []
        for route in self._routes:
            if start in route.locations:
                if end in route.locations:
                    if route.locations.index(start) < route.locations.index(end):
                        founded_routes.append(route)
        return '\n'.join(f'{route}' for route in founded_routes)
    
    def find_route_by_id(self, route_id: int) -> Route:
        found_route = [route for route in self._routes if route.id == route_id]
        if found_route == []:
            return None
        return found_route[0]
    
    def find_package_by_id(self, package_id: int) -> Package:
        found_package = [package for package in self._packages if package.id == package_id]
        if found_package == []:
            return None
        return found_package[0]

    def find_truck_by_id(self, truck_id: int) -> Truck:
        found_truck = [truck for truck in self._trucks if truck.id == truck_id]
        if found_truck == []:
            return None
        return found_truck[0]

    def assign_package(self, route_id: int, package_id: int) -> None:
        route = self.find_route(route_id)
        package = self.find_package(package_id)
        if route and package: # /!\ check if it works
            route.add_package(package)
            self._packages.remove(package)

    def assign_truck(self, route_id: int, truck_id: int) -> None:
        route = self.find_route(route_id)
        truck = self.find_truck(truck_id)
        if route and truck: # /!\ check if it works
            route.assign_truck(truck)

    def get_routes_info(self) -> str:
        if self._routes == []:
            return 'No routes available!'
        return '\n'.join(f'Route ID: {route.id} - {route.info()}' for route in self._routes)

    def get_trucks_info(self) -> str:
        if self._trucks == []:
            return 'No trucs available!'
        return '\n---------------\n'.join(f'{truck}' for truck in self._trucks)
        

    def get_packages_info(self) -> str:
        if self._packages == []:
            return 'No packages available!'
        return '\n************\n'.join(f'{package}' for package in self._packages)
        