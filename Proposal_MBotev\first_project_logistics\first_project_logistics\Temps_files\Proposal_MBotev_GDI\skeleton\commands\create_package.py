from core.application_data import ApplicationData
from models.package import Package

# from commands.validation_helpers import try_parse_int, validate_params_count, try_parse_float



class CreatePackageCommand:

    def __init__(self, params, app_data: ApplicationData):
        # validate_params_count(params, 6)
        self._params = params
        self._app_data = app_data

    def execute(self):
        star_location, end_location, weight, *contact_info, = self._params
        package=Package(star_location,end_location,weight,contact_info)
        self._app_data.pending_packages.create_package(package)
        return f'Package with ID: {package.id} to End location:{package.end_location} was created!'



    
