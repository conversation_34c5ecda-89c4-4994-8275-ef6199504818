import unittest
from models.actros import Actros

VALID_NAME='Actros'
VALID_CAPACITY=23000
VALID_MAX_RANGE=10000

class ActrosShould(unittest.TestCase):

    def test_initializer_RaisesErrorWhenIdIsLower(self):
        with self.assertRaises(ValueError):
            astros=Actros(1025,VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)

    def test_initializer_RaisesErrorWhenIdIsHigher(self):
        with self.assertRaises(ValueError):
            astros=Actros(1041,VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)
