from src.linked_list_node import LinkedListNode


class DoublyLinkedList:
    def __init__(self):
        self._head: LinkedListNode = None
        self._tail: LinkedListNode = None
        self._count = 0

    @property
    def count(self):
        return self._count

    @property
    def head(self):
        return self._head

    @property
    def tail(self):
        return self._tail


    def add_first(self, value):
        if self._head == None:
            self._head = self._tail = LinkedListNode(value)
            self._count += 1
        else:
            self._insert_before_head(value)


    def add_last(self, value):
        if self._tail == None:
            self._head = self._tail = LinkedListNode(value)
            self._count += 1
        else:
            self._insert_after_tail(value)
            

    def insert_after(self, node, value):
        self._check_is_empty()
        if node == self._tail:
            self._insert_after_tail(value)

        else:
            new_node = LinkedListNode(value)
            self._count += 1

            node.next.prev = new_node
            new_node.next = node.next
            node.next = new_node
            new_node.prev = node
            


    def insert_before(self, node, value):
        self._check_is_empty() 
        if node == self._head:
            self._insert_before_head(value)

        else:
            new_node = LinkedListNode(value)
            self._count += 1

            node.prev.next = new_node
            new_node.prev = node.prev
            node.prev = new_node
            new_node.next = node
            

        

    def remove_first(self):
        self._check_is_empty()
        self._count -= 1
        if self._head.prev == None:
            temp = self._head
            self._head = None
            return temp.value
        
        removed = self._head
        self._head = self._head.next
        self._head.prev = None

        return removed.value


    def remove_last(self):
        self._check_is_empty()
        self._count -= 1
        if self._tail.next == None:
            temp = self._tail
            self._tail = None
            return temp.value
        
        removed = self._tail
        self._tail = self._tail.prev
        self._tail.next = None
        
        return removed.value


    def find(self, value):
        if self._head == None:
            return None
        head = self._head
        while head != None:
            if head.value == value:
                return head
            head = head.next


    def values(self):
        listy = []
        head = self._head
        while head != None:
            listy.append(head.value)
            head = head.next
        return tuple(listy)


    def _insert_before_head(self, value):
        new_node = LinkedListNode(value)
        new_node.next = self._head
        self._head.prev = new_node
        self._head = new_node
        self._count += 1


    def _insert_after_tail(self, value):
        new_node = LinkedListNode(value)
        new_node.prev = self._tail
        self._tail.next = new_node
        self._tail = new_node
        self._count += 1


    def _check_is_empty(self):
        if self._head == None:
            raise ValueError("Linked list is empty.")