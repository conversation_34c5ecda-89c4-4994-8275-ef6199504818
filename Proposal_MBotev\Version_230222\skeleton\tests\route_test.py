import unittest
from datetime import datetime, timedelta
from models.route import Route
from models.package import Package
from models.truck import Truck

VALID_DATE='Oct 10th 06:00h'
VALID_LOCATIONS='BRI->SYD->MEL'

VALID_START_LOCATION='SYD'
VALID_END_LOCATION='MEL'
VALID_WEIGHT=45.0
VALID_CONTACT_INFO='<PERSON>, 23, Silver route, Melbourne'

VALID_NAME='Scania'
VALID_CAPACITY=23000
VALID_MAX_RANGE=10000

class Route_Should(unittest.TestCase):

    def test_properties_SetCorrectly(self):
        route1=Route(VALID_DATE, VALID_LOCATIONS)

        self.assertIsInstance(route1.departure_time,datetime)
        self.assertIsInstance(route1.locations, tuple)
        self.assertIsInstance(route1.packages, tuple)
        self.assertIsInstance(route1.trucks, tuple)
        self.assertEqual(1,route1.id)
        self.assertEqual(1786,route1.total_distance)
        self.assertEqual(0,route1.current_route_weight)
        self.assertEqual(0,route1.weight_capacity)

    def test_addpackage_AddPAckageToRoute(self):
        route1=Route(VALID_DATE, VALID_LOCATIONS)
        package1=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)

        route1.add_package(package1)

        self.assertEqual(1,len(route1.packages))

    def test_findpackageinroute_returnNoneIfNoPackage(self):
        route1=Route(VALID_DATE, VALID_LOCATIONS)
        package1=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)

        self.assertIsNone(route1.find_package_in_route(1),None)
    
    def test_findpackageinroute_returnPackageIfExists(self):
        route1=Route(VALID_DATE, VALID_LOCATIONS)
        package1=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)
        route1.add_package(package1)

        self.assertEqual(route1.find_package_in_route(1),package1)

    def test_addtruck_AddTruck(self):
        route1=Route(VALID_DATE, VALID_LOCATIONS)
        truck1=Truck(VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)

        route1.add_truck(truck1)

        self.assertEqual(1,len(route1.trucks))

    def test_currentsituation_RaisesValueErrorIfNoPackage(self):
        route1=Route(VALID_DATE, VALID_LOCATIONS)

        with self.assertRaises(ValueError):
            route1.current_situation(1,VALID_DATE)

    def test_currentsituation_ReturnCorrectString(self):
        route1=Route(VALID_DATE, VALID_LOCATIONS)
        package1=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)
        route1.add_package(package1)
        route1.stops_time_table()


        txt=route1.current_situation(1, 'Oct 11th 09:38h')
        expected_output='The package is already delivered!'
        
        self.assertEqual(expected_output, txt)
        
        txt2=route1.current_situation(1, 'Oct 10th 20:38h')
        expected_output2='The package will be delivered on Oct 11th 09:38h in 13h00min.'
        
        self.assertEqual(expected_output2, txt2)

    def test_nextstop_returnsCorrectString(self):
        route1=Route(VALID_DATE, VALID_LOCATIONS)
        route1.stops_time_table()

        txt=route1.next_stop('Oct 10th 20:38h')
        expected_output=' Next stop-> MEL in 13h00min.'
        
        self.assertEqual(expected_output, txt)

    def test_info_packages_in_route_returnsCorrectString(self):
        route1=Route(VALID_DATE, VALID_LOCATIONS)
        package1=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)
        route1.add_package(package1)

        txt=route1.info_packages_in_route()
        expected_output='#Package ID:1'
        
        self.assertEqual(expected_output, txt)

    def test_info_packages_in_route_returnsEmptyStringIfNoPackages(self):
        route1=Route(VALID_DATE, VALID_LOCATIONS)
        
        txt=route1.info_packages_in_route()
        expected_output=''
        
        self.assertEqual(expected_output, txt)

    def test_str_ReturnCorrectString(self):
        route1=Route(VALID_DATE, VALID_LOCATIONS)
        expected_output='Route ID: 1 | BRI (Oct 10th 06:00h) -> SYD (Oct 10th 20:00h) -> MEL (Oct 11th 09:38h)'
        
        self.assertEqual(expected_output, str(route1))



        
        







