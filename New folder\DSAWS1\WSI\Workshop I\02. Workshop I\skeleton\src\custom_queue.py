
from src.linked_list_node import LinkedListNode


class CustomQueue:
    def __init__(self):
        self._head = None
        self._tail = None
        self._size = 0
    
    def enqueue(self, value):
        new_node = LinkedListNode(value)
        if self._tail is None:
            self._head = new_node
            self._tail = new_node
        else:
            self._tail.next = new_node
            self._tail = new_node
        self._size += 1
    
    def dequeue(self):
        if not self.is_empty:
            value = self._head.value
            self._head = self._head.next
            self._size -= 1
            if self._head is None:
                self._tail = None
            return value
        raise ValueError('Queue is empty')
    
    def peek(self):
        if not self.is_empty:
            return self._head.value
        raise ValueError('Queue is empty')
    
    @property
    def count(self):
        return self._size
    
    @property
    def is_empty(self):
        return self._size == 0
