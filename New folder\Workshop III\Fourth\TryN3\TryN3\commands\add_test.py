from commands.base_command import BaseCommand
from core.application_data import ApplicationData
from models.test import Test

class AddTest(BaseCommand):
    counter_id = 0
    def __init__(self, params: list[str], app_data: ApplicationData):
        AddTest.counter_id += 1
        self._counter = AddTest.counter_id
        super().__init__(params, app_data)
    
    def execute(self):
        new_test = Test(self.counter_id, self._params[1])
        test_group = self._app_data.find_test_group_by_id(int(self._params[0]))
        test_group._tests.append(new_test)
        return f'Test #{new_test.id} added to group #{test_group.id}'


#addtest 1 ShouldWork_WhenToldTo