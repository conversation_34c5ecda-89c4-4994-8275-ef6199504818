import unittest
import tests.test_data as td
from core.application_data import ApplicationData
from models.test_group import TestGroup
from models.test import Test


class AppData_Should(unittest.TestCase):
    def test_canInstantiate(self):
        app_data = ApplicationData()

    def test_init_createsEmptyList(self):
        app_data = ApplicationData()
        self.assertIsInstance(app_data._test_groups, list)
        self.assertEqual(app_data._test_groups, [])

    def test_groupsProperty_returnsCorrect(self):
        app_data = ApplicationData()
        self.assertTupleEqual(app_data.groups, ())

        test_group_one = TestGroup(td.VALID_ID, td.VALID_NAME)
        test_group_two = TestGroup(td.VALID_ID_TWO, td.VALID_NAME_TWO)
        app_data._test_groups.append(test_group_one)
        app_data._test_groups.append(test_group_two)

        self.assertTupleEqual(app_data.groups, (test_group_one, test_group_two))

    def test_findGroupMethodWhen_groupFound(self):
        app_data = ApplicationData()
        test_group_one = TestGroup(td.VALID_ID, td.VALID_NAME)

        app_data._test_groups.append(test_group_one)
        output = app_data.find_group(td.VALID_ID)

        self.assertEqual(output, test_group_one)

    def test_findGroupMethodWhen_groupNotFound(self):
        app_data = ApplicationData()
        test_group_one = TestGroup(td.VALID_ID, td.VALID_NAME)

        app_data._test_groups.append(test_group_one)
        output = app_data.find_group(td.INVALID_ID)

        self.assertEqual(output, None)

    def test_findTestMethodWhen_testFoundReturnsTest(self):
        app_data = ApplicationData()
        test_one = Test(td.VALID_ID, td.VALID_DESCRIPTION)
        test_two = Test(td.VALID_ID_TWO, td.VALID_DESCRIPTION_TEST)
        test_group = TestGroup(td.VALID_ID, td.VALID_GROUP_NAME)
        test_group.add_test(test_one)
        test_group.add_test(test_two)

        app_data._test_groups.append(test_group)
        output = app_data.find_test(td.VALID_ID)

        self.assertEqual(output, test_one)

    def test_findTestMethodWhen_testNotFoundReturnsNone(self):
        app_data = ApplicationData()
        test_one = Test(td.VALID_ID, td.VALID_DESCRIPTION)
        test_two = Test(td.VALID_ID_TWO, td.VALID_DESCRIPTION_TEST)
        test_group = TestGroup(td.VALID_ID, td.VALID_GROUP_NAME)
        test_group.add_test(test_one)
        test_group.add_test(test_two)

        app_data._test_groups.append(test_group)
        output = app_data.find_test(td.INVALID_ID)

        self.assertEqual(output, None)

    def test_addGroupMethod_returnsTrueAndAddsNewGroupWhen_newGroupDoestnExist(self):
        app_data = ApplicationData()
        test_group_one = TestGroup(td.VALID_ID, td.VALID_GROUP_NAME)
        test_group_two = TestGroup(td.VALID_ID_TWO, td.VALID_GROUP_NAME_TWO)

        output = app_data.add_group(test_group_one)
        output = app_data.add_group(test_group_two)

        self.assertTrue(output)
        self.assertIn(test_group_one, app_data.groups)
        self.assertIn(test_group_two, app_data.groups)

    def test_addGroupMethod_returnsFalse_newGroupAldreadyExists(self):
        app_data = ApplicationData()
        test_group_one = TestGroup(td.VALID_ID, td.VALID_GROUP_NAME)

        output = app_data.add_group(test_group_one)
        output = app_data.add_group(test_group_one)

        self.assertIn(test_group_one, app_data.groups)
        self.assertFalse(output)

    def test_removeGroupMethod_returnsTrueAndRemovesGroupWhen_groupExists(self):
        app_data = ApplicationData()
        test_group_one = TestGroup(td.VALID_ID, td.VALID_GROUP_NAME)
        output = app_data.add_group(test_group_one)

        self.assertIn(test_group_one, app_data.groups)

        expected_result = app_data.remove_group(td.VALID_ID)
        self.assertTrue(expected_result)
        self.assertNotIn(test_group_one, app_data.groups)

    def test_removeGroupMethod_returnsFalse_whenGroupDoesntExists(self):
        app_data = ApplicationData()

        
        expected_result = app_data.remove_group(td.INVALID_ID)
        self.assertFalse(expected_result)
        


    
        


    