# def get_item(item, id_parcels, parcel_number):
#     if parcel_number: 
#         if parcel_number == id_parcels[item]:
#             print("data of parcel_number")
#             return get_data(parcel_number)
#         else:
#             return None

#     else:
#         parcel_number = id_parcels[item]
#         print("all data")
#         return get_data(parcel_number)


def get_item(item, id_parcels, parcel_number):
    if not parcel_number:
        parcel_number = id_parcels[item]
        print("all data")
    elif parcel_number == id_parcels[item]:
        print("data of parcel_number")
    else:
        return 

    return 'data'



def get_data(parcel_number):
    return "data"


parcel_number = 0
id_parcels = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

for item, v in enumerate(id_parcels):
    result = get_item(item, id_parcels, parcel_number)
    print(result)
