from models.test_group import TestGroup


class ApplicationData:
    def __init__(self):
        self._test_groups: list[TestGroup] = []

    @property
    def groups(self):
        return tuple(self._test_groups)

    def add_group(self, test_group_id: int, test_group_name: str):
        new_test_group = TestGroup(test_group_id, test_group_name)
        self._test_groups.append(new_test_group)

    @property
    def test_groups(self):
        return self._test_groups

