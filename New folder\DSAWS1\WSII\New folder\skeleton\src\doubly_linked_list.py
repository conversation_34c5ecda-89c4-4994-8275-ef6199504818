from src.linked_list_node import LinkedListNode


class DoublyLinkedList:
    def __init__(self):
        self._head: LinkedListNode = None
        self._tail: LinkedListNode = None
        self._count = 0

    @property
    def count(self):
        return self._count

    @property
    def head(self):
        return self._head

    @property
    def tail(self):
        return self._tail

    def add_first(self, value):
        new_node = LinkedListNode(value=value)
        if self.head is None:
            self._head = new_node
            self._tail = new_node
            self._count += 1
        else:
            self.head.prev = new_node
            new_node.next = self.head
            self._head = new_node
            self._count += 1
            
    def add_last(self, value):
        new_node = LinkedListNode(value=value)
    
        if self.tail is None:
            self._tail = new_node
            self._head = new_node
            self._count += 1
        else:
            current = self.tail
            current.next = new_node
            self._tail = new_node
            self._tail.prev = current
            self._count += 1

    def insert_after(self, node, value):
        if self.count == 0 :
            raise ValueError("empty")
        new_node = LinkedListNode(value=value)
        if node.next is None:
            new_node.prev = node
            new_node.next = node.next
            node.next = new_node
            self._tail = new_node
            self._count +=1
        else:
        
            new_node.prev = node
            new_node.next = node.next
            node.next = new_node
            self._count +=1
        # to be continue      
        

    def insert_before(self, node, value):
        
        if self.count == 0 :
            raise ValueError("empty")    
        new_node = LinkedListNode(value=value)
        if node.prev is None:
            self._head = new_node
            self._count +=1 
            self._head.next = node
        else:
            new_node.prev = node.prev
            node.prev.next = new_node
            new_node.next = node
            self._count +=1
            




    def remove_first(self):
        if self._head is not None:
            current_value = self._head.value
            self._head = self._head.next
            self._count -= 1
            return current_value
        raise ValueError("It is empty!")

        
            

    def remove_last(self):
        if self.tail is None:
            raise ValueError("It is empty!")
        else:
            current_tail = self.tail
            self._tail = self._tail.prev
            self._count -=1
        return current_tail.value

    def find(self, value):
        current = self.head
        while current is not None:
            if current.value == value:
                return current
            current = current.next
        return None

    def values(self):
        t = []
        current = self.head
        while current is not None:
            t.append(current.value)
            current = current.next
        return tuple(t)

    def _insert_before_head(self, value):
        raise NotImplementedError()

    def _insert_after_tail(self, value):
        raise NotImplementedError()

