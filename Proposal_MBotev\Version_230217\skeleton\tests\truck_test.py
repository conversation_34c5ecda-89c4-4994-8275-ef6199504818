import unittest
from models.truck import Truck

VALID_TRUCK_ID=1001
VALID_NAME='Scania'
VALID_CAPACITY=23000
VALID_MAX_RANGE=10000
EXPECTED_OUTPUT='\n'.join([
                f'Truck ID: {VALID_TRUCK_ID}',
                f'Name: {VALID_NAME}',
                f'Capacity: {VALID_CAPACITY}',
                f'Max range: {VALID_MAX_RANGE}',
        ])


class TruckShould(unittest.TestCase):
    def test_initializerSetsProperties(self):
        #Arrange & Act
        truck=Truck(VALID_TRUCK_ID,VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)
        #Assert
        self.assertEqual(VALID_TRUCK_ID,truck.id)
        self.assertEqual(VALID_NAME,truck.name)
        self.assertEqual(VALID_CAPACITY,truck.capacity)
        self.assertEqual(VALID_MAX_RANGE,truck.max_range)


    def test_str_returnsCorrectly(self):
        truck=Truck(VALID_TRUCK_ID,VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)
        self.assertEqual(EXPECTED_OUTPUT,str(truck))