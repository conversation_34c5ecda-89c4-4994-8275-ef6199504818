from src.custom_queue import CustomQueue
from src.custom_stack import CustomStack
from src.linked_list_node import LinkedListNode


a = LinkedListNode(None)
b = CustomStack()

# print(b.count)
# # print(b.peek())
# print(b.is_empty)

# b.push(7)

# print(b.count)
# print(b.peek())
# print(b.is_empty)

c = CustomQueue()

# print(c.count)
# # print(c.peek())
# print(c.is_empty)

c.enqueue(6)
print(c.count)
print(c.is_empty)
print(c.peek())

c.enqueue(7)
print(c.peek())
print(c.count)

c.dequeue()
print(c.peek())
