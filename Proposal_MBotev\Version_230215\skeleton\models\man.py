from models.truck import Truck

class Man(Truck):

    # COUNTER=1011
    def __init__(self,id: int, name: str, capacity=37000, max_range=10000):
        super().__init__(id, name, capacity, max_range)
        # id = Man.COUNTER
        # Man.COUNTER+=1
        if id < 1011:
            raise ValueError('The Man id cannot be less than 1011')
        if id > 1025:
            raise ValueError('The Man id cannot be more than 1025')
        
        # self._name='Man'
        # self._capacity=37000
        # self._max_range=10000