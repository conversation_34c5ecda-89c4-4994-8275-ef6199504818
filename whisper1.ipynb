{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import whisper"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["model=whisper.load_model(\"medium\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["sound_file=r\"C:\\Users\\<USER>\\OneDrive\\Документи\\Sound recordings\\2024-01-17 13-56-44.mp4\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\whisper_env\\Lib\\site-packages\\whisper\\transcribe.py:115: UserWarning: FP16 is not supported on CPU; using FP32 instead\n", "  warnings.warn(\"FP16 is not supported on CPU; using FP32 instead\")\n"]}], "source": ["result=model.transcribe(sound_file)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'text': ' Това което направих аз е 10 влт на скрина, която си конфигура, и избереш си ли сайз аутпут на мен, но сега на ми е активно, но преди малко беше активно, и като надеш ES за затвърдиш на стройката, там нататък във всички рекордниги излизут на Fullscreen.',\n", " 'segments': [{'id': 0,\n", "   'seek': 0,\n", "   'start': 0.0,\n", "   'end': 13.0,\n", "   'text': ' Това което направих аз е 10 влт на скрина, която си конфигура, и избереш си ли сайз аутпут',\n", "   'tokens': [50364,\n", "    3200,\n", "    13978,\n", "    3898,\n", "    387,\n", "    860,\n", "    36437,\n", "    4165,\n", "    2559,\n", "    1544,\n", "    1997,\n", "    1266,\n", "    740,\n", "    693,\n", "    403,\n", "    1470,\n", "    5239,\n", "    4679,\n", "    1931,\n", "    11,\n", "    3898,\n", "    681,\n", "    860,\n", "    776,\n", "    435,\n", "    45751,\n", "    11294,\n", "    49305,\n", "    11,\n", "    1006,\n", "    38995,\n", "    1135,\n", "    4433,\n", "    776,\n", "    435,\n", "    7444,\n", "    776,\n", "    3183,\n", "    1544,\n", "    2559,\n", "    3767,\n", "    1354,\n", "    3767,\n", "    51014],\n", "   'temperature': 0.0,\n", "   'avg_logprob': -0.49920712720166455,\n", "   'compression_ratio': 1.7836734693877552,\n", "   'no_speech_prob': 0.017515666782855988},\n", "  {'id': 1,\n", "   'seek': 0,\n", "   'start': 13.0,\n", "   'end': 24.0,\n", "   'text': ' на мен, но сега на ми е активно, но преди малко беше активно, и като надеш ES за затвърдиш на стройката, там нататък във всички рекордниги излизут на Fullscreen.',\n", "   'tokens': [51014,\n", "    1470,\n", "    6046,\n", "    11,\n", "    6035,\n", "    776,\n", "    4953,\n", "    386,\n", "    1470,\n", "    13803,\n", "    1997,\n", "    30239,\n", "    1234,\n", "    11,\n", "    6035,\n", "    8048,\n", "    435,\n", "    19499,\n", "    3752,\n", "    1268,\n", "    42169,\n", "    30239,\n", "    1234,\n", "    11,\n", "    1006,\n", "    981,\n", "    38224,\n", "    8469,\n", "    4433,\n", "    12564,\n", "    4396,\n", "    25880,\n", "    859,\n", "    8515,\n", "    481,\n", "    856,\n", "    9919,\n", "    1470,\n", "    18425,\n", "    1700,\n", "    755,\n", "    19808,\n", "    11,\n", "    8223,\n", "    48290,\n", "    2134,\n", "    8515,\n", "    755,\n", "    740,\n", "    8515,\n", "    859,\n", "    2852,\n", "    3903,\n", "    2241,\n", "    22801,\n", "    41412,\n", "    1903,\n", "    15599,\n", "    3943,\n", "    41610,\n", "    3767,\n", "    1470,\n", "    13841,\n", "    12439,\n", "    13,\n", "    51564],\n", "   'temperature': 0.0,\n", "   'avg_logprob': -0.49920712720166455,\n", "   'compression_ratio': 1.7836734693877552,\n", "   'no_speech_prob': 0.017515666782855988}],\n", " 'language': 'bg'}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}], "metadata": {"kernelspec": {"display_name": "whisper_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}