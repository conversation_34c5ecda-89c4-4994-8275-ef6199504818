from models.product import Product
from models.gender import Gender

class Toothpaste(Product):
    def __init__(self, name, brand, price, gender, ingredients):
        if len(name) < 3 or len(name) > 10:
            raise ValueError('')
        if len(brand) < 2 or len(brand) > 10:
            raise ValueError('')
        if price < 0:
            raise ValueError('')
        # self._name = name
        # self._brand = brand
        # self._price = price
        # self._gender = Gender(gender)
        super().__init__(name, brand, price, gender)
        self._ingredients = ingredients

    @property
    def ingredients(self):
        return tuple(self._ingredients)
    
    def to_string(self):
        ingr = ', '.join(self._ingredients)
        return '\n'.join([
            f' #{self.name} {self.brand}',
            f' #Price: ${self.price:.2f}',
            f' #Gender: {self.gender}',
            f' #Ingredients: {ingr}' 
        ])
    
