from models.product import Product
from models.usage_type import UsageType


class Shampoo(Product):
    def __init__(self, name: str, brand: str, price: float, gender: str, milliliters, usage_type):
        super().__init__(name, brand, price, gender)

        UsageType.from_string(usage_type)
        self._usage_type = usage_type
        self.milliliters = milliliters

    @property
    def milliliters(self):
        return self._milliliters

    @milliliters.setter
    def milliliters(self, value):
        if value < 0:
            raise ValueError('Milliliters cannot be negative.')

        self._milliliters = value

    @property
    def usage_type(self):
        return self._usage_type

    def to_string(self) -> str:
        return '\n'.join([
            f' #{self.name} {self.brand}',
            f' #Price: ${self.price:.2f}',
            f' #Gender: {self.gender}',
            f' #Milliliters: {self.milliliters}',
            f' #Usage: {self.usage_type}'
        ])