import unittest
from models.route import Route
from core.application_data import ApplicationData
from commands.create_route import CreateRouteCommand

class TestCreateRoute(unittest.TestCase):

    def setUp(self):
        self.app_data = ApplicationData()

    def test_create_route(self):
        params = ["12/09/2022", "06:00:00", "Alice Springs", "Adelaide", "Melbourne", "Sydney", "Brisbane"]
        create_route_command = CreateRouteCommand(params, self.app_data)
        result = create_route_command.execute()

        self.assertEqual(result, "Route(departure_time='12/09/2022 06:00:00', locations=['Alice Springs', 'Adelaide', 'Melbourne', 'Sydney', 'Brisbane']) was created!")
        self.assertEqual(len(self.app_data.routes), 1)
        self.assertIsInstance(self.app_data.routes[0], Route)
        self.assertEqual(self.app_data.routes[0].departure_time, "12/09/2022 06:00:00")
        self.assertEqual(self.app_data.routes[0].locations, ['Alice Springs', 'Adelaide', 'Melbourne', 'Sydney', 'Brisbane'])

if __name__ == '__main__':
    unittest.main()