def get_item(item, id_parcels, parcel_number):
    if parcel_number and parcel_number == id_parcels[item]:
        print("data of parcel_number")
        return get_data(parcel_number)

    else:
        parcel_number = id_parcels[item]
        print("all data")
        return get_data(parcel_number)


def get_data(parcel_number):
    return "data"


parcel_number = 1
id_parcels = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

for item, v in enumerate(id_parcels):
    result = get_item(item, id_parcels, parcel_number)
