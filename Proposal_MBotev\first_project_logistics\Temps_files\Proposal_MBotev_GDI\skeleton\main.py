
from core.application_data import ApplicationData
from core.command_factory import CommandFactory
from core.engine import Engine

app_data = ApplicationData()
cmd_factory = CommandFactory(app_data)
engine = Engine(cmd_factory)

engine.start()

"""
CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreateRoute Oct 12th 06:00h SYD->MEL->ADL
CreatePackage SYD MEL 45.0 <PERSON>, 1,Kings road, Melborne
SearchRoute SYD MEL
AssignPackageToRoute 1 1
CreateRoute Sep 12th 06:00h ASP->ADL->MEL->SYD->BRI
CreatePackage ASP BRI 23000.0 Jeremy Inc., 23,Salt lake road, Brisbane
SearchRoute ASP BRI
AssignPackageToRoute 3 2
ViewPendingPackages
ViewRoutes
end

CreateRoute Oct 10th 06:00h BRI->SYD->MEL
CreatePackage SYD MEL 45.0 <PERSON>, 1,Kings road, Melborne
ViewPendingPackages
ViewRoutes
AssignPackageToRoute 1 1
ViewRoutes
ViewPendingPackages
end

"""
