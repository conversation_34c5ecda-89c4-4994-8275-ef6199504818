
from src.linked_list_node import LinkedListNode


class CustomQueue:
    def __init__(self) -> None:
        self.head:LinkedListNode = None
        self.tail:LinkedListNode = None
        self.count = 0
        self.is_empty = True

    def enqueue(self,value):
        new_node = LinkedListNode(value)
        if self.is_empty is True:
            self.head = new_node
        else:
            self.tail.next = new_node
        self.tail = new_node
        self.count += 1
        self.is_empty = False

    def dequeue(self):
        if self.is_empty is True:
            raise ValueError('Queue is empty')
        popped_node = self.head
        self.head = self.head.next
        self.count -= 1
        return popped_node.value
    
    def peek(self):
        if self.is_empty is True:
            raise ValueError('Queue is empty')
        return self.head.value


        
