from commands.base.base_command import BaseCommand
from models.test_run import TestRun

class AddTestRun(BaseCommand):
    def execute(self):
        test_id, result, runtime = self._params
        test_id = int(test_id)
        runtime = int(runtime)
        new_test_run = TestRun(result, runtime)
        test = self.app_data.find_test(test_id)
        test.add_test_run(new_test_run)
        return f'TestRun registered'