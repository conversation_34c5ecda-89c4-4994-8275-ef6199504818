
from core.application_data import ApplicationData
from models.route import Route
from models.package import Package
from models.pending_packages import PendingPackages

app_data = ApplicationData()
first_route = Route('Oct 10th 06:00h', 'BRI->SYD->MEL')
app_data.add_route(first_route)

second_route = Route('Oct 12th 06:00h', 'SYD->MEL->ADL')
app_data.add_route(second_route)
third_route=Route('Sep 12th 06:00h','ASP->ADL->MEL->SYD->BRI')
app_data.add_route(third_route)
# print(app_data.routes)
print(app_data.search_route('SYD','MEL'))

print(app_data.find_route(3))

first_package=Package('SYD','MEL',45.0,'<PERSON>, 1,Kings road, Melborne')
# # print(first_package)
# app_data.create_package(first_package)
app_data.pending_packages.create_package(first_package)
print(app_data.assign_package(1,1))

# print(first_route.packages[0])
print(first_route.current_route_weight)
      
second_package=Package('ASP','BRI',23000.0,'Jeremy Inc., 23,Salt lake road, Brisbane')
app_data.pending_packages.create_package(second_package)
print(app_data.assign_package(3,2))
# # print(second_package)
# print(third_route.packages[0])
print(third_route.current_route_weight)
print(app_data.pending_packages)
