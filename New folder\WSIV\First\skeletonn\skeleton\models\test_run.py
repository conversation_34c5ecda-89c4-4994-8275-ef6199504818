from errors.params_error import InvalidParamsTestRunClass
from models.constants.test_result import TestResult


class TestRun:
    def __init__(self, test_result: TestResult, runtime_ms: int):
        if runtime_ms <= 0:
            raise InvalidParamsTestRunClass()

        self._test_result = test_result
        self._runtime_ms = runtime_ms

    @property
    def test_result(self):
        return self._test_result

    @property
    def runtime_ms(self):
        return self._runtime_ms


