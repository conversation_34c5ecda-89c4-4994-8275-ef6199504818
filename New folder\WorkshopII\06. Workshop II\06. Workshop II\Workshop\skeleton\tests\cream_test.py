import unittest

from models.gender import Gender
from models.cream import Cream
from models.scent import Scent

VALID_NAME = 'TestName'
VALID_BRAND = 'TestBrand'
VALID_PRICE = 3.5
VALID_GENDER = Gender.UNISEX
VALID_SCENT = Scent.ROSE

EXPECTED_OUTPUT = f''' #{VALID_NAME} {VALID_BRAND}
 #Price: ${VALID_PRICE:.2f}
 #Gender: {VALID_GENDER}
 #Scent: {VALID_SCENT}'''


class cream_Should(unittest.TestCase):
    def test_constructor_raisesError_when_nameLengthOutOfBounds(self):
        with self.assertRaises(ValueError):
            _ = Cream('_', VALID_BRAND, VALID_PRICE, VALID_GENDER, VALID_SCENT)

    def test_constructor_raisesError_when_brandLengthOutOfBounds(self):
        with self.assertRaises(ValueError):
            _ = Cream(VALID_NAME, '_', VALID_PRICE, VALID_GENDER, VALID_SCENT)

    def test_constructor_raiseError_when_priceIsNegative(self):
        with self.assertRaises(ValueError):
            _ = Cream(VALID_NAME, VALID_BRAND, -1.5, VALID_GENDER, VALID_SCENT)
    
    def test_constructor_raiseError_when_genderIsInvalid(self):
        with self.assertRaises(ValueError):
            _ = Cream(VALID_NAME, VALID_BRAND, VALID_PRICE, 'not a gender', VALID_SCENT)

    def test_constructor_setsProperties_whenArgumentsAreValid(self):
        cream = Cream(VALID_NAME, VALID_BRAND, VALID_PRICE, VALID_GENDER, VALID_SCENT)
        self.assertEqual(VALID_NAME, cream.name)
        self.assertEqual(VALID_BRAND, cream.brand)
        self.assertEqual(VALID_PRICE, cream.price)
        self.assertEqual(VALID_GENDER, cream.gender)
        self.assertEqual(VALID_SCENT, cream.scent)

    def test_toString_returnsCorrectlyFormattedString(self):
        # Arrange
        cream = Cream(VALID_NAME, VALID_BRAND, VALID_PRICE, VALID_GENDER, VALID_SCENT)

        # Act & Assert
        self.assertEqual(EXPECTED_OUTPUT, cream.to_string())
       
