from datetime import datetime

class Package:
    """
    This class creates Package object with attributes: 
    start_location, end_location, weight, contact information
    Properties: Id, registration time, start_location, end_location, weight, contact information
    """
    COUNTER=1
    FORMAT="%b %dth %H:%Mh"

    def __init__(self,start_location: str,end_location: str,weight: float,contact_info: str) -> None:
        self._id=Package.COUNTER
        Package.COUNTER+=1
        self._start_location=start_location
        self._end_location=end_location
        self._weight=weight
        self._contact_info=contact_info
        self._registration_time=datetime.now()

    @property
    def id(self):
        return self._id
    
    @property
    def registration_time(self):
        return self._registration_time
    
    @property
    def start_location(self):
        return self._start_location
    
    @property
    def end_location(self):
        return self._end_location
    
    @property
    def weight(self):
        return self._weight
    
    @property
    def contact_info(self):
        return self._contact_info
    
    def __str__(self) -> str:
        return '\n'.join([
                f'Package ID: {self.id}',
                f'Date of registration: {datetime.strftime(self.registration_time,Package.FORMAT)}',
                f'Start location: {self.start_location}',
                f'End Location: {self.end_location}',
                f'Weight: {self.weight}',
                f'Contact information: {self.contact_info}',
        ])
    
    
   

    
    


# first_package=Package('SYD','MEL',45,'John Smith, 1,Kings road, Melborne')
# print(first_package)

# second_package=Package('ASP','BRI',23000.0,'Jeremy Inc., 23,Salt lake road, Brisbane')
# print(second_package)

