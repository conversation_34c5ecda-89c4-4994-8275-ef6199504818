from datetime import datetime, timedelta
from models.constants.distance import distance
from models.package import Package
from models.truck import Truck



class Route:
    """
    Creates Route object with attributes: 
    -departure_time as string with format 'Oct 10th 06:00h"
    -list of locations as string, separated with '->' example: 'SYD->MEL->ADL'.
    Properties: Id, Derarture time, Locations,Total distance, Courrant route weight, Weihgt capacity, Packages,Trucks, Stops_time table as dictionary, 
    containing stop locations with corresponding times.
    """
    COUNTER = 1
    FORMAT = "%b %dth %H:%Mh"

    def __init__(self, departure_time: str, locations: str) -> None:
        self._id = Route.COUNTER
        Route.COUNTER += 1
        self._locations = locations.split('->')
        self._departure_time = datetime.strptime(departure_time, Route.FORMAT)
        self._trucks: list[Truck] = []
        self._packages: list[Package] = []
        self._stops: dict = {}

    @property
    def departure_time(self):
        return self._departure_time
    
    @property
    def locations(self):
        return tuple(self._locations)

    @property
    def total_distance(self) -> int:
        return sum(self.intermediate_distances())

    def stops_time_table(self) -> dict:
        for i in range(len(self._locations)):
            self._stops[self._locations[i]] = datetime.strftime(self.times()[i], Route.FORMAT)
        return self._stops

    @property
    def id(self):
        return self._id
    
    @property
    def current_route_weight(self) -> int:
        return sum([p.weight for p in self._packages])

    @property
    def packages(self):
        return tuple(self._packages)

    @property
    def weight_capacity(self) -> int:
        return sum([t.capacity for t in self._trucks])

    @property
    def trucks(self):
        return tuple(self._trucks)

    def add_package(self, package: Package) -> None:
        self._packages.append(package)

    def find_package_in_route(self,package_id: int)->Package:
        for package in self._packages:
            if package.id == package_id:
                return package

    def add_truck(self, truck: Truck) -> None:
        self._trucks.append(truck)

    def current_situation(self,package_id: int,request_time: datetime)->str:
        r_time=datetime.strptime(request_time, Route.FORMAT)
        package=self.find_package_in_route(package_id)
        if package==None:
            raise ValueError('No package found!')
        delivery_location=package.end_location
        for key in self._stops.keys():
            if key == delivery_location:
                stop_time=datetime.strptime(self._stops[key], Route.FORMAT)
                if stop_time<=r_time:
                    return f'The package is already delivered!'
                elif stop_time>r_time:
                    lap_time='h'.join(str(stop_time-r_time).split(':')[:2])
                    return f'The package will be delivered on {self._stops[key]} in {lap_time}min.'

    def next_stop(self,request_time: str)-> str:
        r_time=datetime.strptime(request_time, Route.FORMAT)

        for key,value in self._stops.items():
            stop_time=datetime.strptime(value, Route.FORMAT)
            if stop_time>r_time:
                lap_time='h'.join(str(stop_time-r_time).split(':')[:2])
                return f' Next stop-> {key} in {lap_time}min.'
          


    def end_time(self, start_time, distance) -> datetime:
        return start_time + timedelta(hours=(distance / 87 + 3.56))

    def intermediate_distances(self) -> list:
        inter_distances = []
        for i in range(len(self._locations) - 1):
            inter_distances.append(distance(self._locations[i], self._locations[i + 1]))
        return inter_distances

    def times(self) -> list:
        times = [self._departure_time]
        i = 0
        for k in self.intermediate_distances():
            times.append(self.end_time(times[i], k))
            i += 1
        return times

    def info_packages_in_route(self):
        return '\n'.join(f'#Package ID:{p.id}' for p in self._packages)

    def __str__(self) -> str:
        stops = ' -> '.join(f'{stops} ({time})' for stops, time in self.stops_time_table().items())
        return f'Route ID: {self._id} | {stops}'



