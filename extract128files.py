# import os

# def get_files(directory, num_files):
#     files = os.listdir(directory)
#     return files[:num_files]

# # Usage
# files = get_files(r"C:\Garot\Inference_data\DATA", 128)

import os
import shutil

def copy_files(source_directory, target_directory, num_files):
    files = os.listdir(source_directory)
    for file in files[:num_files]:
        shutil.copy(os.path.join(source_directory, file), target_directory)

# Usage
copy_files(r"C:\Garot\Inference_data\DATA", r"C:\Garot\Inference_data\DATA128", 128)
