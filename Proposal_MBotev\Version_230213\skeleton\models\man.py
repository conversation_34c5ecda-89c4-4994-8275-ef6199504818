from models.truck import Truck

class Man(Truck):

    COUNTER=1011
    def __init__(self, name: str, capacity: int, max_range: int):
        super().__init__(id, name, capacity, max_range)
        id = Man.COUNTER
        Man.COUNTER+=1
        if Man.COUNTER > 1025:
            raise ValueError('No more Man availbale!')
        self._name='Man'
        self._capacity=37000
        self._max_range=10000