from models.toothpaste import Toothpaste
from models.shampoo import Shampoo
from models.cream import Cream
# toothpaste_=Toothpaste('Superwhite','Colgate',10,'Unisex',['lavander'])
# print(toothpaste_.price)
# toothpaste_.to_string()
# shampoo=Shampoo('Fresh','Nivea','3.5','Unisex','Medical','10')
# print(shampoo._usage_type)
# print(shampoo.milliliters)
# print(shampoo.price)
cream=Cream('ime','marka','10','Women','rose')
print(cream.brand)

