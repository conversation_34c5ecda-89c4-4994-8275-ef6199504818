import copy


def create_list_with_two(two_value):
    """Create a list that uses the current value of 'two'"""
    return [1, two_value, 3, 4, 5, 6, 7, 8, 9, 10]


# Initial value of two
two = 2
test_list = create_list_with_two(two)
print(f"Original list with two={two}: {test_list}")

# Create a copy of the list
altered_list = copy.copy(test_list)
print(f"Copied list: {altered_list}")

# Change the value of two
two = 5
print(f"Changed two to: {two}")

# Create a new list that reflects the updated value of two
updated_list = create_list_with_two(two)
print(f"Updated list with two={two}: {updated_list}")

# Update the altered_list to reflect the new value of two
altered_list[1] = two  # Update the element at index 1 (which was 'two')
print(f"Altered list after updating two: {altered_list}")
