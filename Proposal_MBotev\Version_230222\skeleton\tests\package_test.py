import unittest
from models.package import Package


VALID_START_LOCATION='SYD'
VALID_END_LOCATION='MEL'
VALID_WEIGHT=23.0
VALID_CONTACT_INFO='<PERSON>, 23, Silver route, Melbourne'
EXPECTED_OUTPUT='\n'.join([
                f'Package ID: 1',
                f'Start location: {VALID_START_LOCATION}',
                f'End Location: {VALID_END_LOCATION}',
                f'Weight: {VALID_WEIGHT}',
                f'Contact information: {VALID_CONTACT_INFO}',
        ])

class PackageShould(unittest.TestCase):

    def test_initializerSetsProperties(self):
        #Arrange & Act
        package=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)
        #Assert
        self.assertEqual(VALID_START_LOCATION,package.start_location)
        self.assertEqual(VALID_END_LOCATION,package.end_location)
        self.assertEqual(VALID_WEIGHT,package.weight)
        self.assertEqual(VALID_CONTACT_INFO,package.contact_info)
        self.assertEqual(1,package.id)

    def test_initializerSetsConsequitiveID(self):
        package1=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)
        package2=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)
        package3=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)

        self.assertEqual(1,package1.id)
        self.assertEqual(2,package2.id)
        self.assertEqual(3,package3.id)



    def test_str_returnsCorrectly(self):
        package=Package(VALID_START_LOCATION,VALID_END_LOCATION,VALID_WEIGHT,VALID_CONTACT_INFO)

        self.assertEqual(EXPECTED_OUTPUT,str(package))