import unittest
import tests.test_data as td
from models.test_group import TestGroup
from errors.params_error import InvalidParamsTestGroupClass
from models.test import Test


class TestGroup_Should(unittest.TestCase):
    def test_canInstantiate(self):
        test_group = TestGroup(td.VALID_ID, td.VALID_NAME)

    def test_initializer_setsValuesValid(self):
        test_group = TestGroup(td.VALID_ID, td.VALID_NAME)
        self.assertEqual(test_group.id, td.VALID_ID)
        self.assertEqual(test_group.name, td.VALID_NAME)

    def test_init_raisesErrorWhen_nameIsEmpty(self):
        with self.assertRaises(InvalidParamsTestGroupClass):
            test_group = TestGroup(td.VALID_ID, td.INVALID_EMPTY_NAME)
            self.assertEqual("", td.INVALID_EMPTY_NAME)

    def test_init_raisesErrorWhen_nameIsNone(self):
        with self.assertRaises(InvalidParamsTestGroupClass):
            test_group = TestGroup(td.VALID_ID, td.INVALID_NONE_NAME)
            self.assertEqual(None, td.INVALID_EMPTY_NAME)

    def test_init_createsEmptyList(self):
        test_group = TestGroup(td.VALID_ID, td.VALID_NAME)
        self.assertEqual([], test_group._tests)
            
    def test_addTest_addsTestToTestList(self):
        test = Test(999, td.TEST_DESCRIPTION)
        test_group = TestGroup(td.VALID_ID, td.VALID_NAME)
        test_group.add_test(test)

    def test_magicStringMethod_returnsCorrectString(self):
        test_group = TestGroup(td.VALID_ID, td.VALID_NAME)
        self.assertEqual(td.EXPECTED_OUTPUT, test_group.__str__() )

    def test_viewMethod_returnsCorrectString(self):
        test_group = TestGroup(td.VALID_ID, td.VALID_NAME)
        self.assertEqual(td.EXPECTED_OUTPUT, test_group.view())