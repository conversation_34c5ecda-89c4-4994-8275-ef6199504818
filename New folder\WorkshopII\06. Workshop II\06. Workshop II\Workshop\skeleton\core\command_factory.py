from commands.add_to_category import AddTo<PERSON>ategoryCommand
from commands.add_to_shopping_cart import AddToShopping<PERSON>artCommand
from commands.create_category import Create<PERSON>ategoryCommand
from commands.create_shampoo import CreateShampooCommand
from commands.create_toothpaste import Create<PERSON><PERSON>hpasteCommand
from commands.remove_from_category import Remove<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ommand
from commands.remove_from_shopping_cart import Remove<PERSON>romShoppingCartCommand
from commands.show_category import ShowCategoryCommand
from commands.total_price import TotalPriceCommand
from commands.create_cream import CreateCreamCommand


class CommandFactory:
    def __init__(self, data):
        self._app_data = data

    def create(self, input_line):
        cmd, *params = input_line.split()

        if cmd.lower() == "createcategory":
            return CreateCategoryCommand(params, self._app_data)
        if cmd.lower() == "createshampoo":
            return CreateShampooCommand(params, self._app_data)
        if cmd.lower() == "createtoothpaste":
            return CreateToothpasteCommand(params, self._app_data)
        if cmd.lower() == "showcategory":
            return ShowCategoryCommand(params, self._app_data)
        if cmd.lower() == "addtocategory":
            return AddToCategoryCommand(params, self._app_data)
        if cmd.lower() == "removefromcategory":
            return RemoveFromCategoryCommand(params, self._app_data)
        if cmd.lower() == "addtoshoppingcart":
            return AddToShoppingCartCommand(params, self._app_data)
        if cmd.lower() == "removefromshoppingcart":
            return RemoveFromShoppingCartCommand(params, self._app_data)
        if cmd.lower() == "totalprice":
            return TotalPriceCommand(self._app_data)
        if cmd.lower() == 'createcream':
            return CreateCreamCommand(params,self._app_data)

        raise ValueError(f'Invalid command name: {cmd}!')
