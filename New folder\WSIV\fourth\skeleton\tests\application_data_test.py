import unittest
from core.application_data import ApplicationData
from models.test import Test
from models.test_group import TestGroup

class ApplicationData_Should(unittest.TestCase):
    def test_initiallize_successfully(self):
        appData = ApplicationData()

        self.assertEqual(0,len(appData._test_groups))
    
    def test_returnTuple_whenGroupPropretyIsCalled(self):
        appData = ApplicationData()
        t_group = TestGroup(1,"My Group")
        appData._test_groups.append(t_group)
        self.assertTrue(type(appData.groups) is tuple)
    
    def test_returnNone_usingFindGroupMethod_whenNoAddedGroups(self):
        appData = ApplicationData()
        
        self.assertEqual(None, appData.find_group(1))

    def test_returnCorrectGroup_usingFindGroupMethod(self):
        appData = ApplicationData()
        t_group = TestGroup(1,"My Group")
        appData._test_groups.append(t_group)
        
        finded_group = appData.find_group(1)

        self.assertEqual("My Group", finded_group.name)
    
    def test_returnTest_withFindTestMethod_ifGroupAndTestExists(self):
        appData = ApplicationData()
        t_group = TestGroup(1,"My Group")
        tst = Test(10, "Lorem Ipsum")
 
        t_group._tests.append(tst)
        appData._test_groups.append(t_group)

        searched_test = appData.find_test(10)
        self.assertEqual(10, searched_test.id)

    def test_returnNone_withFindTestMethod_ifGroupExists_TestDoNotExists(self):
        appData = ApplicationData()
        t_group = TestGroup(1,"My Group")
        appData._test_groups.append(t_group)

        searched_test = appData.find_test(10)

        self.assertEqual(None, searched_test)

    
    def test_returnTrue_withAddMethod_whenGroupIsNotAlreadyAdded(self):
        appData = ApplicationData()
        t_group = TestGroup(1,"My Group")

        result = appData.add_group(t_group)

        self.assertEqual(True,result)
    
    def test_addsGroup_withAddMethod_whenGroupIsNotAlreadyAdded(self):
        appData = ApplicationData()
        t_group = TestGroup(1,"My Group")
        
        appData.add_group(t_group)

        self.assertEqual(1, len(appData.groups))

    def test_returnFalse_withAddMethod_whenGroupIsAlreadyAdded(self):
        appData = ApplicationData()
        t_group = TestGroup(1,"My Group")
        t_group_2 = TestGroup(1,"My Group")

        appData.add_group(t_group)
        result = appData.add_group(t_group_2)

        self.assertEqual(False,result)
    
    def test_checksIfGroupIsAdded_withAddMethod_whenGroupIsAlreadyAdded(self):
        appData = ApplicationData()
        t_group = TestGroup(1,"My Group")
        t_group_2 = TestGroup(1,"My Group")

        appData.add_group(t_group)
        appData.add_group(t_group_2)

        self.assertEqual(1, len(appData.groups))
    
    
    def test_removesGroup_ifGroupExists(self):
        appData = ApplicationData()
        t_group = TestGroup(1,"My Group")
        appData._test_groups.append(t_group)
        appData.remove_group(1)

        self.assertEqual(0, len(appData.groups))
    
    def test_returnsTrue_usingRemoveGroupMethod_ifGroupExists(self):
        appData = ApplicationData()
        t_group = TestGroup(1,"My Group")
        appData._test_groups.append(t_group)
        result = appData.remove_group(1)

        self.assertTrue(result)
    
    def test_removesGroup_ifGroupDoesNotExists(self):
        appData = ApplicationData()
        t_group = TestGroup(1,"My Group")
        appData._test_groups.append(t_group)
        appData.remove_group(2)

        self.assertEqual(1, len(appData.groups))

    def test_returnsFalse_usingRemoveGroupMethod_ifGroupDoesNotExists(self):
        appData = ApplicationData()
        t_group = TestGroup(1,"My Group")
        appData._test_groups.append(t_group)
        result = appData.remove_group(2)

        self.assertEqual(False, result)
