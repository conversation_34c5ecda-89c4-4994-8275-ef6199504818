import unittest
import tests.test_data as td

from errors.invalid_command import Invalid<PERSON>ommandFactoryClass
from core.application_data import ApplicationData
from core.command_factory import CommandFactory
from core.models_factory import ModelsFactory
from commands.add_test import AddTestCommand
from commands.add_test_group import AddTestGroupCommand
from commands.remove_group import Remove<PERSON><PERSON><PERSON><PERSON>mand
from commands.test_report import TestReportCommand
from commands.view_group import View<PERSON><PERSON><PERSON>ommand
from commands.view_system import ViewSystemCommand
from commands.add_test_run import AddTestRunCommand


class CommandFactory_Should(unittest.TestCase):
    def test_canInstantiate(self):
        app_data = ApplicationData()
        command_factory = CommandFactory(app_data)
        self.assertIs(command_factory._app_data, app_data)
        self.assertIsInstance(command_factory._models_factory, ModelsFactory)       

    def test_createMethodWhen_newCommandIsSplit(self):
        app_data = ApplicationData()
        command_factory = CommandFactory(app_data)
        cmd, *params = td.ADDTEST_INPUT_LINE.split()

        result = command_factory.create(td.ADDTEST_INPUT_LINE)
        self.assertEqual(cmd, "addTest")
        self.assertEqual(params, ["testParams", "testGroupParams"])

    def test_createMethod_raisesErrorWhen_invalidCommand(self):
        with self.assertRaises(InvalidCommandFactoryClass):
            app_data = ApplicationData()
            command_factory = CommandFactory(app_data)
            cmd = command_factory.create(td.INVALID_COMMAND)

    def test_createMethodWhen_commandAddTest(self):
        app_data = ApplicationData()
        command_factory = CommandFactory(app_data)
        cmd = command_factory.create(td.ADDTEST_INPUT_LINE)
        self.assertIsInstance(cmd, AddTestCommand)
        self.assertIs(cmd.app_data, app_data)
        self.assertIsInstance(cmd.models_factory, ModelsFactory)
        self.assertEqual(cmd.params, ("testParams", "testGroupParams"))

    def test_createMethodWhen_commandAddTestGroup(self):
        app_data = ApplicationData()
        command_factory = CommandFactory(app_data)
        cmd = command_factory.create(td.ADDTESTGROUP_INPUT_LINE)
        self.assertIsInstance(cmd, AddTestGroupCommand)
        self.assertIs(cmd.app_data, app_data)
        self.assertIsInstance(cmd.models_factory, ModelsFactory)
        self.assertEqual(cmd.params, ("1", "BigGroup"))

    def test_createMethodWhen_commandRemoveGroup(self):
        app_data = ApplicationData()
        command_factory = CommandFactory(app_data)
        cmd = command_factory.create(td.REMOVEGROUP_INPUT_LINE)
        self.assertIsInstance(cmd, RemoveGroupCommand)
        self.assertIs(cmd.app_data, app_data)
        self.assertEqual(cmd.params, ("1",))

    def test_createMethodWhen_commandAddTestRun(self):
        app_data = ApplicationData()
        command_factory = CommandFactory(app_data)
        cmd = command_factory.create(td.ADDTESTRUN_INPUT_LINE)
        self.assertIsInstance(cmd, AddTestRunCommand)
        self.assertIs(cmd.app_data, app_data)
        self.assertIsInstance(cmd.models_factory, ModelsFactory)
        self.assertEqual(cmd.params, ("testName", "1" ,"pass"))

    def test_createMethodWhen_commandTestReport(self):
        app_data = ApplicationData()
        command_factory = CommandFactory(app_data)
        cmd =command_factory.create(td.TESTREPORT_INPUT_LINE)
        self.assertIsInstance(cmd, TestReportCommand)

    def test_createMethodWhen_commandViewGroup(self): 
        app_data = ApplicationData()
        command_factory = CommandFactory(app_data)
        cmd = command_factory.create(td.VIEWGROUP_INPUT_LINE)
        self.assertIsInstance(cmd, ViewGroupCommand)
        self.assertIs(cmd.app_data, app_data)
        self.assertEqual(cmd.params, ("1",))

    def test_createMethodWhen_commandViewSystem(self): 
        app_data = ApplicationData()
        command_factory = CommandFactory(app_data)
        cmd = command_factory.create(td.VIEWSYSTEM_INPUT_LINE)
        self.assertIsInstance(cmd, ViewSystemCommand)
        self.assertIs(cmd.app_data, app_data)
        self.assertEqual(cmd.params, ("1",))

    






        