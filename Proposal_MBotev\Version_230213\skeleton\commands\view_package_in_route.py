from core.application_data import ApplicationData

# from commands.validation_helpers import validate_params_count


class ViewPackageInRouteCommand:

    def __init__(self, params, app_data: ApplicationData):
        # validate_params_count(params, 1)
        self._params = params
        self._app_data = app_data

    def execute(self):
        package_id=int(self._params[0])
        request_time = str(' '.join(self._params[1:]))
        route=self._app_data.route_containg_package(package_id)
        current_state=route.current_situation(package_id,request_time)
        return f'Package with ID:{package_id}, travelling on {route}. Current state: {current_state}'