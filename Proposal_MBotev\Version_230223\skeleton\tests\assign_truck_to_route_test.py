import unittest
from datetime import datetime
from core.application_data import ApplicationData
from commands.assign_truck_to_route import AssignTruckToRouteCommand
from models.truck import Truck
from models.route import Route


class AssignTruckToRouteCommandTests(unittest.TestCase):
    def setUp(self):
        self.app_data = ApplicationData()
        self.route = Route('Oct 10th 06:00h', 'BRI->SYD->MEL')
        self.truck = Truck(1, 'Actros', 5000, 500)

    def test_execute_assigns_truck_to_route(self):
        self.app_data.truck_parking.create_truck(self.truck)
        self.app_data.add_route(self.route)

        assign_command = AssignTruckToRouteCommand([self.route.id, self.truck.name], self.app_data)
        result = assign_command.execute()

        self.assertEqual(result, f"Truck: {self.truck.name} assigned to Route with ID: {self.route.id}!")
        self.assertEqual(len(self.route.trucks), 1)
        self.assertEqual(self.route.trucks[0], self.truck)
        self.assertEqual(len(self.app_data.truck_parking.available_trucks), 0)

    def test_execute_raises_value_error_if_route_not_found(self):
        self.app_data.truck_parking.create_truck(self.truck)

        assign_command = AssignTruckToRouteCommand([self.route.id, self.truck.name], self.app_data)
        with self.assertRaises(ValueError):
            assign_command.execute()

    def test_execute_raises_value_error_if_truck_not_found(self):
        self.app_data.add_route(self.route)

        assign_command = AssignTruckToRouteCommand([self.route.id, self.truck.name], self.app_data)
        with self.assertRaises(ValueError):
            assign_command.execute()

    def test_execute_raises_value_error_if_truck_capacity_is_not_sufficient(self):
        self.truck.capacity = 1000
        self.app_data.truck_parking.create_truck(self.truck)
        self.app_data.add_route(self.route)
        self.route.add_package(1, 'BRI', 'MEL', 1000, datetime.now())

        assign_command = AssignTruckToRouteCommand([self.route.id, self.truck.name], self.app_data)
        with self.assertRaises(ValueError):
            assign_command.execute()

    def test_execute_raises_value_error_if_truck_max_range_is_not_sufficient(self):
        self.truck.max_range = 100
        self.app_data.truck_parking.create_truck(self.truck)
        self.app_data.add_route(self.route)
        self.route.add_package(1, 'BRI', 'MEL', 1000, datetime.now())

        assign_command = AssignTruckToRouteCommand([self.route.id, self.truck.name], self.app_data)
        with self.assertRaises(ValueError):
            assign_command.execute()

if __name__ == '__main__':
    unittest.main()
