from models.truck import Truck

class TruckParking:
    """
    Contains all trucks that are still not assign to a route.
    """
    def __init__(self) -> None:
        self._available_trucks: list[Truck]=[]

    @property
    def available_trucks(self):
        return tuple(self._available_trucks)
    
    def create_truck(self,truck: Truck)->None:
        self._available_trucks.append(truck)
    
    def find_truck(self,truck_name: str)->Truck:
        founded=[truck for truck in self._available_trucks if truck.name == truck_name]
        if founded == []:
            raise ValueError('No Trucks available!')
        return founded[0]
    
    def remove_truck(self,truck: Truck) -> str:
        self._available_trucks.remove(truck)
        

    def __str__(self) -> str:
        if self._available_trucks == []:
            return f'/!\ No Trucks available. Have a break!'
        trucks_in_parking = '\n**************\n'.join(f'#Truck ID:{truck.id} | Name: {truck.name} | Capacity: {truck.capacity} | Max range: {truck.max_range}' for truck in self._available_trucks)
        return f'  Available free trucks Information:\n'+f'{trucks_in_parking}'