from commands.create_package import Create<PERSON>ackageCommand
from commands.create_route import <PERSON><PERSON><PERSON>out<PERSON><PERSON><PERSON>mand
from commands.search_route import Search<PERSON>oute<PERSON>ommand
from commands.add_truck_to_parking import AddTruckToParkingCommand
from commands.assign_truck_to_route import AssignTruckToRouteCommand
from commands.assign_package_to_route import AssignPackageToRouteCommand
from commands.view_routes import View<PERSON>out<PERSON><PERSON><PERSON>mand
from commands.view_pending_packages import ViewPendingPackagesCommand
from commands.view_package_in_route import ViewPackageInRouteCommand
from commands.view_trucks import ViewTrucksCommand

class CommandFactory:
    def __init__(self, data):
        self._app_data = data

    def create(self, input_line):
        cmd, *params = input_line.split()

        if cmd.lower() == "addtrucktoparking":
            return AddTruckToParkingCommand(params, self._app_data)
        if cmd.lower() == "createpackage":
            return CreatePackageCommand(params, self._app_data)
        if cmd.lower() == "createroute":
            return CreateRouteCommand(params, self._app_data)
        if cmd.lower() == "searchroute":
            return SearchRouteCommand(params, self._app_data)
        if cmd.lower() == "assigntrucktoroute":
            return AssignTruckToRouteCommand(params, self._app_data)
        if cmd.lower() == "assignpackagetoroute":
            return AssignPackageToRouteCommand(params, self._app_data)
        if cmd.lower() == "viewroutes":
            return ViewRoutesCommand(params, self._app_data)
        if cmd.lower() == "viewpendingpackages":
            return ViewPendingPackagesCommand(params, self._app_data)
        if cmd.lower() == "viewpackageinroute":
            return ViewPackageInRouteCommand(params, self._app_data)
        if cmd.lower() == "viewtrucks":
            return ViewTrucksCommand(params, self._app_data)
        

        raise ValueError(f'Invalid command name: {cmd}!')
