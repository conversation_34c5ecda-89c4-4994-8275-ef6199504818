import unittest
from unittest.mock import Mock

from commands.create_shampoo import CreateShampooCommand


def _create_fake_params(
        *,
        name='MyMan',
        brand='TrashyOverpricedBrand',
        price='10.99',
        gender='Men',
        milliliters='1000',
        usage_type='Every_Day'):
    return [name, brand, price, gender, milliliters, usage_type]


def _create_mock(*, product_exists_return_value: bool):
    fake_data = Mock()

    def product_exists(name):
        return product_exists_return_value

    def create_shampoo(name, brand, price, gender, usage_type, milliliters):
        product = Mock()
        product.name = name
        fake_data.products.append(product)

        return product

    fake_data.products = []
    fake_data.product_exists = product_exists
    fake_data.create_shampoo = create_shampoo

    return fake_data


class CreateShampooCommandTest_Should(unittest.TestCase):
    def test_initializer_raisesError_tooFewParamsCount(self):
        # Arrange, Act & Assert
        with self.assertRaises(ValueError):
            cmd = CreateShampooCommand(['a'] * 5, Mock())

    def test_initializer_raisesError_tooManyParamsCount(self):
        # Arrange, Act & Assert
        with self.assertRaises(ValueError):
            cmd = CreateShampooCommand(['a'] * 7, Mock())

    def test_initializer_passes_validParamsCount(self):
        # Arrange & Act
        CreateShampooCommand(['a'] * 6, Mock())

        # Assert
        # nothing to assert

    def test_initializer_createsShampoo_validParams(self):
        # Arrange
        fake_params = _create_fake_params()
        cmd = CreateShampooCommand(
            fake_params,
            _create_mock(product_exists_return_value=False))

        # Act
        output = cmd.execute()

        # Assert
        self.assertEqual(
            f'Shampoo with name {fake_params[0]} was created!', output)

    def test_initializer_raisesError_productExists(self):
        # Arrange
        cmd = CreateShampooCommand(
            _create_fake_params(),
            _create_mock(product_exists_return_value=True))

        # Act & Assert
        with self.assertRaises(ValueError):
            cmd.execute()

    def test_initializer_raisesError_invalidPrice(self):
        # Arrange
        cmd = CreateShampooCommand(
            _create_fake_params(price='not a float'),
            _create_mock(product_exists_return_value=False))

        # Act & Assert
        with self.assertRaises(ValueError):
            cmd.execute()

    def test_initializer_raisesError_invalidGender(self):
        # Arrange
        cmd = CreateShampooCommand(
            _create_fake_params(gender='not a gender'),
            _create_mock(product_exists_return_value=False))

        # Act & Assert
        with self.assertRaises(ValueError):
            cmd.execute()

    def test_initializer_raisesError_invalidMilliliters(self):
        # Arrange
        cmd = CreateShampooCommand(
            _create_fake_params(milliliters='not an int'),
            _create_mock(product_exists_return_value=False))

        # Act & Assert
        with self.assertRaises(ValueError):
            cmd.execute()

    def test_initializer_raisesError_invalidUsageType(self):
        # Arrange
        cmd = CreateShampooCommand(
            _create_fake_params(usage_type='not a usage_type'),
            _create_mock(product_exists_return_value=False))

        # Act & Assert
        with self.assertRaises(ValueError):
            cmd.execute()
