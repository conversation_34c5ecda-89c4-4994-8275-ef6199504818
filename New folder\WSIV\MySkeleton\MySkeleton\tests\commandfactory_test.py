import unittest
from unittest.mock import MagicMock
from commands.add_test_group import AddTest<PERSON><PERSON><PERSON>ommand
from commands.add_test_run import AddTestRunCommand
from commands.remove_group import RemoveGroupCommand
from commands.test_report import TestReportCommand
from commands.view_group import <PERSON><PERSON><PERSON>Command
from commands.view_system import ViewSystem<PERSON>ommand
from core.application_data import ApplicationData
from commands.add_test import AddTestCommand
from core.command_factory import CommandFactory
from core.models_factory import ModelsFactory
from errors.application_error import ApplicationError

class CommandFactory_Should(unittest.TestCase):
    def setUp(self):
        self.data = ApplicationData()
        self.factory = CommandFactory(self.data)

    def test_create_add_test(self):
        cmd = self.factory.create("addtest test_name group_name")
        self.assertIsInstance(cmd, AddTestCommand)
        self.assertEqual(cmd.params, ["test_name", "group_name"])
        self.assertEqual(cmd.app_data, self.app_data)
        self.assertIsInstance(cmd.models_factory, ModelsFactory)

    def test_create_add_test_group(self):
        cmd = self.factory.create("addtestgroup group_name")
        self.assertIsInstance(cmd, AddTestGroupCommand)
        self.assertEqual(cmd.params, ["group_name"])
        self.assertEqual(cmd.app_data, self.app_data)
        self.assertIsInstance(cmd.models_factory, ModelsFactory)

    def test_create_remove_group(self):
        cmd = self.factory.create("removegroup group_name")
        self.assertIsInstance(cmd, RemoveGroupCommand)
        self.assertEqual(cmd.params, ("group_name"))
        self.assertEqual(cmd.app_data, self.app_data)

    def test_create_add_test_run(self):
        cmd = self.factory.create("addtestrun test_name group_name status time")
        self.assertIsInstance(cmd, AddTestRunCommand)
        self.assertEqual(cmd.params, ["test_name", "group_name", "status", "time"])
        self.assertEqual(cmd.app_data, self.app_data)
        self.assertIsInstance(cmd.models_factory, ModelsFactory)

    def test_create_test_report(self):
        cmd = self.factory.create("testreport test_name group_name")
        self.assertIsInstance(cmd, TestReportCommand)
        self.assertEqual(cmd.params, ["test_name", "group_name"])
        self.assertEqual(cmd.app_data, self.app_data)

    def test_create_view_group(self):
        cmd = self.factory.create("viewgroup group_name")
        self.assertIsInstance(cmd, ViewGroupCommand)
        self.assertEqual(cmd.params, ("group_name",))
        self.assertEqual(cmd._app_data, self.data)


    def test_create_view_system(self):
        cmd = self.factory.create("viewsystem")
        self.assertIsInstance(cmd, ViewSystemCommand)
        self.assertEqual(cmd.params, ())
        self.assertEqual(cmd._app_data, self.data)

    def test_create_add_test_group(self):
        cmd = self.factory.create("addtestgroup group_name")
        self.assertIsInstance(cmd, AddTestGroupCommand)
        self.assertEqual(cmd.params, ("group_name",))
        self.assertEqual(cmd._app_data, self.data)
        self.assertEqual(cmd._models_factory, self.factory._models_factory)

    def test_create_remove_group(self):
        cmd = self.factory.create("removegroup group_name")
        self.assertEqual(cmd.params, ("group_name",))


    def test_create_add_test_run(self):
        cmd = self.factory.create("addtestrun group_name test_name test_status test_run_time")
        self.assertIsInstance(cmd, AddTestRunCommand)
        self.assertEqual(cmd.params, ("group_name", "test_name", "test_status", "test_run_time"))
        self.assertEqual(cmd._app_data, self.data)
        self.assertEqual(cmd._models_factory, self.factory._models_factory)

    def test_create_test_report(self):
        cmd = self.factory.create("testreport group_name test_name")
        self.assertIsInstance(cmd, TestReportCommand)
        self.assertEqual(cmd.params, ("group_name", "test_name"))
        self.assertEqual(cmd._app_data, self.data)

    def test_create_add_test(self):
        cmd = self.factory.create("addtest group_name test_name")
        self.assertIsInstance(cmd, AddTestCommand)
        self.assertEqual(cmd.params, ("group_name", "test_name"))
        self.assertEqual(cmd._app_data, self.data)
        self.assertEqual(cmd._models_factory, self.factory._models_factory)

    def test_create_invalid_command(self):
        with self.assertRaises(ApplicationError):
            self.factory.create("invalid_command")