
 
**Project Description**

Design and implement a Logistics console application.
The application will be used by employees of a large Australian company aiming to expand its activities to the freight industry. The app will be used to manage the delivery of packages between hubs in major Australian cities. An employee of the company must be able to record the details of a delivery package, create or search for suitable delivery routes, and inspect the current state of delivery packages, transport vehicles and delivery routes. 
Functional Requirements
The application must support the following operations:
1.	Creating a delivery package – unique id, start location, end location and weight in kg, and contact information for the customer.
2.	Creating a delivery route – should have a unique id, and a list of locations (at least two).
a.	The first location is the starting location – it has a departure time.
b.	The other locations have expected arrival time.
3.	Search for a route based on package’s start and end locations.
4.	Updating a delivery route – assign a free truck to it. 
5.	Updating a delivery route – assign a delivery package.
6.	View a information about routes, packages and trucks.

The application should support the following operations:
1.	Save the application state to the file system


The company owns the following transport vehicles:
Vehicle ids	Name	Capacity (kg)	Max range (km)	Number of vehicles
1001-1010	Scania	        42000	           8000	    10
1011-1025	Man	            37000	          10000	    15
1026-1040	Actros	        26000	          13000	    15




 


Use the following distances in km
	SYD	    MEL	    ADL	    ASP	    BRI	    DAR	    PER
SYD		    877	    1376	2762	909	    3935	4016
MEL	877		        725	    2255	1765	3752	3509
ADL	1376	725		        1530	1927	3027	2785
ASP	2762	2255	1530		    2993	1497	2481
BRI	909	    1765	1927	2993		    3426	4311
DAR	3935	3752	3027	1497	3426		    4025
PER	4016	3509	2785	2481	4311	4025	



Use cases

Use case #1
A customer visits the company office in Sydney on Oct 8th. They bring a package that needs to be delivered to Melbourne. An employee of the company records the customer’s contact info, weighs the package at 45kg and then checks for a suitable delivery route. The system reports that there are two routes:
-	Brisbane (Oct 10th 06:00h) → Sydney (Oct 10th 20:00h) → Melbourne (Oct 11th 18:00h)
-	Sydney (Oct 12th 06:00h) → Melbourne (Oct 12th 20:00h) → Adelaide (Oct 13th 15:00h)
Both routes' trucks have free capacity, and the employee suggests the first one, as the package will arrive one day earlier. The customer agrees and the employee uses the system to add the delivery package to the first route and to update the package’s expected arrival time to Oct 11th 18:00h.

Use case #2
Many packages with total weight of 23000kg have gathered in the hub in Alice Springs and an employee of the company uses the system to create a route that leaves on Sep 12th 06:00h with the following stops:
-	Alice Springs → Adelaide → Melbourne → Sydney → Brisbane
The system determines the route distance to 4041km and calculates estimated arrival times for each of the locations based on a predefined average speed of 87km/h. The employee then finds a free truck that meets the required range and capacity and proceeds to bulk assign the packages to the newly created route by using the route id and the packages’ ids.

Use case #3
A manager at the company uses the system to find information about all delivery routes in progress. The system responds with information that contains each route’s stops, delivery weight, and the expected current stop based on the time of the day.

Use case #4
A supervising employee uses the system to view information about each package that is not yet assigned to a delivery route. The system responds with a list of packages containing their IDs and locations

Use case #5
A customer contacts the office to request information about their package. The customer provides the id that they received when the package was created, and an employee enters the package id in the system. It responds with detailed information which is then emailed to the customer.

Technical Requirements
•	Follow the OOP programming principles:
o	Encapsulate your objects.
o	Apply information hiding where necessary.
o	Decide between inheritance and composition properly.
o	Use polymorphism properly.
•	Follow guidelines for writing readable code:
o	Adequate naming of variables, functions, classes, methods, and attributes.
o	Well-formatted and consistent code.
o	Well-structured and readable logic.
•	Implement proper user input validation and display meaningful user messages.
•	Implement proper error handling.
•	Prefer using list comprehension where readability will be improved
•	Cover the core functionality with unit tests.
•	Use Git to keep your source code and for team collaboration.


Teamwork Guidelines

Step 1 - Create your repository
•	Create a new group repository on GitLab.
•	Add your teammate as a group member.
•	Create a new project in the group and mark it as private. Add your trainers as members (with Reporter role). Adding the trainers as members will send them email notification with url of the repo.
•	Create a README.md file in your repository with details about your project.

Step 2 – Plan your tasks
How you organize you work is crucial to the success of the team. Create a GitLab Issues board with the following data, fill it and keep it updated:
You can have several columns (lists) in the board:
•	Open – the backlog of your project, containing all open issues
•	To do – issues that have been assigned and scheduled with a deadline
•	Doing – what issues are currently in progress (no more than 2 per person)
•	For review – the issues that need to be reviewed by your teammates
•	Closed – all issues that are done
You can use different issues for different purposes:
•	Features:
o	Name - the name of the card would be the given feature/task that needs to be done.
o	Size - what is the size of the feature in terms of programming effort i.e., Large/Medium/Small.
o	Priority - what is the importance of the feature i.e., Must/Should/Could.
o	Owner - who is responsible for the successful completion of the given feature/task.
•	Bugs:
o	Name - the name of the card would be the given issue/problem of the software that need to be fixed.
o	Size - what is the size of the feature in terms of programming effort i.e., Large/Medium/Small.
o	Priority - what is the importance of the feature i.e., Must/Should/Could.
o	Owner - who is responsible for the successful completion of the given feature/task.
•	Ideas - any additional ideas for new features or improvements in the project.
o	Name - the name of the card would be a short description of the idea that occurred to you.
Reference: GitLab Issue Boards documentation

Step 3 – Coding!
Try to adhere to this project specification and make your project as close to it as possible. Also, do not go crazy on features, implement a few but implement them amazingly!
Always remember, quality over quantity!

Step 4 – Validation.
Review the code and test the behavior of the features of your team members. All team members should be aware how a feature works, even if they were not the ones to implement it.

Step 5 – Teamwork.
It is important that you work as a team towards a common goal:
•	You should prepare a common plan that you agree to follow.
•	You should take responsibility for your tasks.
•	You should communicate and ask for details with regards to the project implementation.
•	You should be able to explain how you have contributed to the project.
•	You should be able to explain the source code of your team members.

Step 6 – Give Feedback about Your Teammates.
You will be asked to provide feedback about your teammates, their attitude to this project, their technical skills, their team working skills, their contribution to the project, etc. The feedback is important part of the project evaluation so take it seriously and be honest.

Working Effectively in Teams

Project Scope and Ground Rules
Establish project objectives, team norms and guidelines. 
To work effectively as a team discussing project objectives and assigning team roles, guidelines and division of labor is critical. Think through who will: 
•	Schedule meetings and make sure you follow a timeline/agenda 
•	Take notes at meetings to send to everyone afterwards 
•	Research topics for the project   
•	Compile and design presentation for partners 
•	Present your project work 

Create a timeline and split work into smaller tasks
A timeline is important to make sure the project isn’t left until the last minute. This will also help estimate work effort and identify dependencies between activities (one cannot start if the other hasn’t finished, etc.) Set aside buffer time in case there are delays in delivery. 
Discuss and assign tasks according to each person’s preferences to make it less overwhelming. This also makes it easier to complete the project work because team members can work independently on their sections. Write off the tasks and time estimates in the project management system in GitLab.
Schedule Meetings 
Your team meetings can be in person or virtual. This will allow you keep yourselves on track and ensure you have an opportunity to share ideas, notes and research.

Discuss communication ground rules. 
However, you decide to organize your communication, it is important to set mutually agreed upon ground rules for contribution. For example, if you miss a meeting, you could be expected to get back to the person in 24 hrs. Or, if you consistently miss meetings/fail to communicate/produce work, you may not be given credit for the project. Life happens. Put yourself in the shoes of your team members. Try and understand where they are coming from and be inclusive as much as possible. 

Peer-to-Peer Feedback
Keep a Positive Mindset
The first thing to remember when giving or receiving feedback is that it’s intended to help. It shouldn’t be viewed as a personal attack. Feedback should be written/delivered with positive intentions: To help your peers grow as they progress along their career journey.
Be Specific and Actionable
When possible, try to provide actionable steps for improvement. For example, rather than saying, “You need to be better about finishing projects on schedule”, you could say, “It would be helpful if you provided progress updates so that we can offer additional support as deadlines approach.”
Focus on Problems, Not People
Again, constructive criticism shouldn’t feel like a personal attack. To avoid defensiveness, you should focus on your peers’ work, not their personality. Writing comments in the passive voice can actually help with this. For example, rather than saying, “You didn’t provide enough data in the presentation,” you could say, “The presentation would be more convincing if it included more data.”






