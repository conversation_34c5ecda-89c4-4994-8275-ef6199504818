from models.test import Test


class TestGroup:
    id_counter = 1

    def __init__(self, name: str):
        self._id = TestGroup.id_counter
        TestGroup.id_counter += 1
        self._name = name
        self._tests: list[Test] = []

    def __eq__(self, __o: object) -> bool:
        return self.name == __o.name

    @property
    def id(self):
        return self._id

    @property
    def name(self):
        return self._name

    @property
    def tests(self):
        return tuple(self._tests)

    def add_test(self, test: Test):
        self._tests.append(test)
