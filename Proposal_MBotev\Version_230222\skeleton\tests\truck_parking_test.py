import unittest
from core.truck_parking import TruckParking
from models.truck import Truck

VALID_TRUCK_ID=1001
VALID_NAME='Scania'
VALID_CAPACITY=23000
VALID_MAX_RANGE=10000

EXPECTED_OUTPUT_No_Trucks='/!\ No Trucks available. Have a break!'
# TRUCKS_IN_PARKING=f'#Truck ID:{VALID_TRUCK_ID} | Name: {VALID_NAME} | Capacity: {VALID_CAPACITY} | Max range: {VALID_MAX_RANGE}'
EXPECTED_OUTPUT_Yes_Trucks='  Available free trucks Information:\n#Truck ID:1001 | Name: Scania | Capacity: 23000 | Max range: 10000'

class TruckParking_Should(unittest.TestCase):
    def test_initializer_SetsPropetyCorrectly(self):
        tr_parking=TruckParking()

        self.assertIsInstance(tr_parking.available_trucks,tuple)

    def test_addtruck_AddsTruckToTuple(self):
        tr_parking=TruckParking()
        truck_to_add=Truck(VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)
        tr_parking.create_truck(truck_to_add)

        self.assertEqual(1,len(tr_parking.available_trucks))

    def test_findtruck_returnsTruckIfAvailable(self):
        tr_parking=TruckParking()
        truck_to_add=Truck(VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)
        tr_parking.create_truck(truck_to_add)

        found_truck=tr_parking.find_truck(VALID_NAME)

        self.assertIsInstance(found_truck,Truck)
    
    def test_findtruck_raiseErrorIfTruckNotfAvailable(self):
        tr_parking=TruckParking()
        truck_to_add=Truck(VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)

        with self.assertRaises(ValueError):
            found_truck=tr_parking.find_truck(VALID_NAME)

    def test_removetruck_ifTruckAvailable(self):
        tr_parking=TruckParking()
        truck_to_add=Truck(VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)
        tr_parking.create_truck(truck_to_add)

        tr_parking.remove_truck(truck_to_add)

        self.assertEqual(0,len(tr_parking.available_trucks))

    def test_str_IfNoTrucks(self):
        tr_parking=TruckParking()
        truck_to_add=Truck(VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)
        tr_parking.create_truck(truck_to_add)
        tr_parking.remove_truck(truck_to_add)

        self.assertEqual(EXPECTED_OUTPUT_No_Trucks, str(tr_parking))
    
    def test_str_IfTrucksAvailable(self):
        tr_parking=TruckParking()
        truck_to_add=Truck(VALID_NAME,VALID_CAPACITY,VALID_MAX_RANGE)
        tr_parking.create_truck(truck_to_add)
        self.assertEqual(EXPECTED_OUTPUT_Yes_Trucks, str(tr_parking))
        





        
