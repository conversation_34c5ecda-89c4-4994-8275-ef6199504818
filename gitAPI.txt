{"url": "https://api.github.com/gists/c245c0b0d550d919125f87028eab3caf", "forks_url": "https://api.github.com/gists/c245c0b0d550d919125f87028eab3caf/forks", "commits_url": "https://api.github.com/gists/c245c0b0d550d919125f87028eab3caf/commits", "id": "c245c0b0d550d919125f87028eab3caf", "node_id": "G_kwDOAygrQdoAIGMyNDVjMGIwZDU1MGQ5MTkxMjVmODcwMjhlYWIzY2Fm", "git_pull_url": "https://gist.github.com/c245c0b0d550d919125f87028eab3caf.git", "git_push_url": "https://gist.github.com/c245c0b0d550d919125f87028eab3caf.git", "html_url": "https://gist.github.com/Armer7/c245c0b0d550d919125f87028eab3caf", "files": {".eslintignore": {"filename": ".es<PERSON><PERSON><PERSON>", "type": "text/plain", "language": "Ignore List", "raw_url": "https://gist.githubusercontent.com/Armer7/c245c0b0d550d919125f87028eab3caf/raw/69e86cb8dd9c786e8d8967e3856cd8687f007a70/.eslintignore", "size": 383}, ".eslintrc.js": {"filename": ".eslintrc.js", "type": "application/javascript", "language": "JavaScript", "raw_url": "https://gist.githubusercontent.com/Armer7/c245c0b0d550d919125f87028eab3caf/raw/e6563b3ea32cc37176dfe173e7b8d0149a4cc2aa/.eslintrc.js", "size": 19091}, "package.json": {"filename": "package.json", "type": "application/json", "language": "JSON", "raw_url": "https://gist.githubusercontent.com/Armer7/c245c0b0d550d919125f87028eab3caf/raw/955315b669c3c153cc1532e5d5f7ec5d8d453821/package.json", "size": 292}}, "public": true, "created_at": "2023-04-04T08:50:41Z", "updated_at": "2023-04-04T08:50:41Z", "description": "ESLint configuration extending Airbnb's and VueJS's configurations (targeting JavaScript and VueJS files).", "comments": 0, "user": null, "comments_url": "https://api.github.com/gists/c245c0b0d550d919125f87028eab3caf/comments", "owner": {"login": "Armer7", "id": 52964161, "node_id": "MDQ6VXNlcjUyOTY0MTYx", "avatar_url": "https://avatars.githubusercontent.com/u/52964161?v=4", "gravatar_id": "", "url": "https://api.github.com/users/Armer7", "html_url": "https://github.com/Armer7", "followers_url": "https://api.github.com/users/Armer7/followers", "following_url": "https://api.github.com/users/Armer7/following{/other_user}", "gists_url": "https://api.github.com/users/Armer7/gists{/gist_id}", "starred_url": "https://api.github.com/users/Armer7/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/Armer7/subscriptions", "organizations_url": "https://api.github.com/users/Armer7/orgs", "repos_url": "https://api.github.com/users/Armer7/repos", "events_url": "https://api.github.com/users/Armer7/events{/privacy}", "received_events_url": "https://api.github.com/users/Armer7/received_events", "type": "User", "site_admin": false}, "truncated": false}