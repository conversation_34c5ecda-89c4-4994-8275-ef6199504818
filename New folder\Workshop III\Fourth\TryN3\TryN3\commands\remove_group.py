from commands.base_command import BaseCommand
from core.application_data import ApplicationData

class RemoveGroup(BaseCommand):
    def __init__(self, params: list[str], app_data: ApplicationData):
        super().__init__(params, app_data)

    def execute(self):
        test_group_id = self._app_data.find_test_group_by_id(int(self._params[0]))
        test_group_id._tests.clear()
        self._app_data._test_groups.remove(test_group_id)
        return f'Group #{test_group_id._id} removed'