import unittest
from core.application_data import ApplicationData
from commands.create_package import CreatePackageCommand

class TestCreatePackage(unittest.TestCase):
    def setUp(self):
        self.app_data = ApplicationData()

    def test_create_package(self):
        params = ['Sydney', 'Melbourne', '45', '<PERSON>', '<EMAIL>', '555-555-5555']
        create_package_command = CreatePackageCommand(params, self.app_data)
        result = create_package_command.execute()
        
        self.assertIn('Package with ID:', result)
        self.assertIn('to End location:Melbourne was created!', result)
        
        # Check if the package was added to the pending_packages list
        self.assertEqual(len(self.app_data.pending_packages), 1)
        
        # Check if the package has the correct attributes
        package = self.app_data.pending_packages[0]
        self.assertEqual(package.start_location, 'Sydney')
        self.assertEqual(package.end_location, 'Melbourne')
        self.assertEqual(package.weight, '45')
        self.assertEqual(package.contact_info, ['<PERSON>', '<EMAIL>', '555-555-5555'])
        
if __name__ == '__main__':
    unittest.main()
    
    
    