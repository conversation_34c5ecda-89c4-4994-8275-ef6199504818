class InvalidParamsTestRunClass(Exception):
    def __init__(self):
        super().__init__(f'{type(self).__name__} class expects 2 parameters.')

class InvalidParamsTestGroupClass(Exception):
    def __init__(self):
        super().__init__(f'Invalid value for TestGroup name')

class InvalidParamsTestClass(Exception):
    def __init__(self):
        super().__init__(f'Invalid value for Test description')