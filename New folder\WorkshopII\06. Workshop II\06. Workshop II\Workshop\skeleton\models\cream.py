from models.product import Product
from models.scent import Scent

#Поздрави на Ники(Николай Димитров) за пренаправените тестове.
#Ники беше пропуснал да направи тест за Cream в application_data_test-a
#и го направих самостоятелно.

class Cream(Product):
    def __init__(self, name, brand, price, gender, scent):
        super().__init__(name, brand, price, gender)
        self.scent=Scent.to_string(scent)


    @property
    def scent(self):
        return self._scent

    @scent.setter
    def scent(self,value):
        self._scent=Scent.to_string(value)

    def to_string(self):

        return '\n'.join([
            f' #{self.name} {self.brand}',
            f' #Price: ${self.price:.2f}',
            f' #Gender: {self.gender}',
            f' #Scent: {self.scent}',
        ])
        
    
    def is_name_valid(self, name):
        if len(name)<3 or len(name)>15:
            raise ValueError('Invalid name length')
        return name
    
    def is_brand_valid(self, brand):
        if len(brand)<3 or len(brand)>15:
            raise ValueError('Invalid brand length')
        return brand
    
    def is_price_valid(self, price):
        if isinstance(float(price),float):
            if float(price)<0:
                raise ValueError('Milliliters cannot be negative !')
            return float(price)
        else:
            raise ValueError('Invalid milliliters !')


# Cream (name, brand, price, gender, scent)
# name minimum 3 symbols and maximum 15
# brand minimum 3 symbols and maximum 15
# price greater than zero
# gender (men, women, unisex)
# scent (lavender, vanilla, rose)
# Implement product creation in the Factory and the ApplicationData
# Just look at the other products
# Test it if it works correctly